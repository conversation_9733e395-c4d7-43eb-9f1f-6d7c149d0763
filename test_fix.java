import java.util.*;

/**
 * Simple test to verify the fix for extractTableRowsFromData method
 */
public class test_fix {
    
    public static void main(String[] args) {
        // Test the row-based extraction logic
        testRowBasedExtraction();
    }
    
    public static void testRowBasedExtraction() {
        System.out.println("Testing row-based data extraction...");
        
        // Simulate the data structure that would be processed
        // Original data: [{"name": "<PERSON>", "age": 25}, {"name": "<PERSON>", "age": 30}]
        List<Map<String, Object>> testData = new ArrayList<>();
        
        Map<String, Object> row1 = new HashMap<>();
        row1.put("name", "<PERSON>");
        row1.put("age", 25);
        testData.add(row1);
        
        Map<String, Object> row2 = new HashMap<>();
        row2.put("name", "<PERSON>");
        row2.put("age", 30);
        testData.add(row2);
        
        // Simulate column configuration
        List<String> columnNames = Arrays.asList("name", "age");
        
        // Test the extraction logic
        List<Map<String, Object>> result = extractTableRowsFromTestData(testData, columnNames);
        
        System.out.println("Expected format:");
        System.out.println("Row 1: {\"name\": \"John\", \"age\": \"25\"}");
        System.out.println("Row 2: {\"name\": \"Jane\", \"age\": \"30\"}");
        
        System.out.println("\nActual result:");
        for (int i = 0; i < result.size(); i++) {
            System.out.println("Row " + (i + 1) + ": " + result.get(i));
        }
        
        // Verify the result
        if (result.size() == 2) {
            Map<String, Object> resultRow1 = result.get(0);
            Map<String, Object> resultRow2 = result.get(1);
            
            if ("John".equals(resultRow1.get("name")) && "25".equals(resultRow1.get("age")) &&
                "Jane".equals(resultRow2.get("name")) && "30".equals(resultRow2.get("age"))) {
                System.out.println("\n✓ Test PASSED: Row-based extraction is working correctly!");
            } else {
                System.out.println("\n✗ Test FAILED: Row data is incorrect");
            }
        } else {
            System.out.println("\n✗ Test FAILED: Expected 2 rows, got " + result.size());
        }
    }
    
    /**
     * Simplified version of the extraction logic to test the concept
     */
    public static List<Map<String, Object>> extractTableRowsFromTestData(List<Map<String, Object>> data, List<String> columnNames) {
        List<Map<String, Object>> tableRows = new ArrayList<>();
        
        // Process each row in the data
        for (Map<String, Object> dataItem : data) {
            Map<String, Object> row = new LinkedHashMap<>();
            
            // Extract each column value for this row
            for (String columnName : columnNames) {
                Object fieldValue = dataItem.get(columnName);
                String displayValue = convertValueToString(fieldValue);
                row.put(columnName, displayValue);
            }
            
            if (!row.isEmpty()) {
                tableRows.add(row);
            }
        }
        
        return tableRows;
    }
    
    /**
     * Convert value to string (simplified version)
     */
    private static String convertValueToString(Object value) {
        if (value == null) {
            return null;
        }
        return String.valueOf(value);
    }
}
