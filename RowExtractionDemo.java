import java.util.*;

/**
 * Demo to show the difference between column-based and row-based extraction
 */
public class RowExtractionDemo {
    
    public static void main(String[] args) {
        System.out.println("=== Row-based vs Column-based Data Extraction Demo ===\n");
        
        // Sample data: array of user objects
        List<Map<String, Object>> users = Arrays.asList(
            createUser("<PERSON>", 25, "<EMAIL>"),
            createUser("<PERSON>", 30, "<EMAIL>"),
            createUser("Bob", 35, "<EMAIL>")
        );
        
        System.out.println("Original Data:");
        for (int i = 0; i < users.size(); i++) {
            System.out.println("  User " + (i + 1) + ": " + users.get(i));
        }
        
        List<String> columns = Arrays.asList("name", "age", "email");
        
        System.out.println("\n--- OLD BEHAVIOR (Column-based extraction) ---");
        System.out.println("Problem: Extracts all values for each column, not row-by-row");
        demonstrateColumnBasedExtraction(users, columns);
        
        System.out.println("\n--- NEW BEHAVIOR (Row-based extraction) ---");
        System.out.println("Solution: Extracts each row as a complete object with all column values");
        demonstrateRowBasedExtraction(users, columns);
        
        System.out.println("\n=== Expected Output Format ===");
        System.out.println("\"data\": [");
        System.out.println("  {\"name\": \"John\", \"age\": \"25\", \"email\": \"<EMAIL>\"},");
        System.out.println("  {\"name\": \"Jane\", \"age\": \"30\", \"email\": \"<EMAIL>\"},");
        System.out.println("  {\"name\": \"Bob\", \"age\": \"35\", \"email\": \"<EMAIL>\"}");
        System.out.println("]");
    }
    
    private static Map<String, Object> createUser(String name, int age, String email) {
        Map<String, Object> user = new LinkedHashMap<>();
        user.put("name", name);
        user.put("age", age);
        user.put("email", email);
        return user;
    }
    
    /**
     * Simulates the OLD problematic behavior
     */
    private static void demonstrateColumnBasedExtraction(List<Map<String, Object>> users, List<String> columns) {
        System.out.println("This would extract entire columns instead of individual rows:");
        
        for (String column : columns) {
            List<Object> columnValues = new ArrayList<>();
            for (Map<String, Object> user : users) {
                columnValues.add(user.get(column));
            }
            System.out.println("  " + column + " column: " + columnValues);
        }
        
        System.out.println("Result: Cannot create proper row objects - data is organized by columns!");
    }
    
    /**
     * Demonstrates the NEW correct behavior
     */
    private static void demonstrateRowBasedExtraction(List<Map<String, Object>> users, List<String> columns) {
        System.out.println("This extracts each row as a complete object:");
        
        List<Map<String, Object>> tableRows = new ArrayList<>();
        
        for (int i = 0; i < users.size(); i++) {
            Map<String, Object> user = users.get(i);
            Map<String, Object> row = new LinkedHashMap<>();
            
            for (String column : columns) {
                Object value = user.get(column);
                row.put(column, String.valueOf(value));
            }
            
            tableRows.add(row);
            System.out.println("  Row " + (i + 1) + ": " + row);
        }
        
        System.out.println("Result: Perfect! Each row contains all column values for that specific record.");
    }
}
