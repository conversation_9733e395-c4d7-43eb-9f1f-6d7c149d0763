package com.dcas.system.report.attachment;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.resource.ClassPathResource;
import cn.hutool.core.map.MapUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.dcas.common.annotation.SchemaSwitch;
import com.dcas.common.model.excel.InventoryAttachmentJsExcel;
import com.dcas.common.model.dto.ExportWordDto;
import com.dcas.common.domain.entity.CoModelAnalysisResult;
import com.dcas.common.model.vo.FormConfigTreeVO;
import com.dcas.common.mapper.CoModelAnalysisResultMapper;
import com.dcas.common.model.vo.QueryProjectOperationExportVo;
import com.dcas.system.report.ReportTypeEnum;
import com.dcas.system.report.ReportUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 江苏专供资产清单
 *
 * <AUTHOR>
 * @date 2024/01/19 16:45
 **/
@RequiredArgsConstructor
@Component
@Slf4j
public class JiangSuAttachmentReport implements AttachmentReportInterface {

    private final CoModelAnalysisResultMapper coModelAnalysisResultMapper;

    @Value("${safety.profile}")
    private String basePath;

    @SchemaSwitch(ExportWordDto.class)
    @Override
    public String exportWord(ExportWordDto dto, QueryProjectOperationExportVo vo) throws Exception {

        String path = String.join(File.separator, basePath, "temp", "附件：数据安全风险计算值.xlsx");
        FileUtil.touch(path);

        //输出结果
        OutputStream out = Files.newOutputStream(Paths.get(path));
        // 附件 资产列表
        //查询资产盘点表
        List<InventoryAttachmentJsExcel> result = queryAssetRisk(dto.getOperationId());
        EasyExcel.write(out).withTemplate(new ClassPathResource("classpath://template/inventoryAttachmentJsTemplate.xlsx").getStream()).sheet().doWrite(result);
        return path;
    }

    @Override
    public void exportWord(HttpServletResponse response, ExportWordDto dto, QueryProjectOperationExportVo vo)
        throws Exception {
        String path = exportWord(dto, vo);
        ReportUtil.output(response, path);
    }

    private List<InventoryAttachmentJsExcel> queryAssetRisk(String operationId) {
        List<CoModelAnalysisResult> coModelAnalysisResultList = coModelAnalysisResultMapper
            .selectList(new QueryWrapper<CoModelAnalysisResult>().eq("operation_id", operationId));

        String titlesStr = "";
        List<Map> resultList = new ArrayList<>();
        for (CoModelAnalysisResult coModelAnalysisResult : coModelAnalysisResultList){
            titlesStr = coModelAnalysisResult.getTitles();
            List<Map> mapList = JSONUtil.toList(coModelAnalysisResult.getResult(), Map.class);
            resultList.addAll(mapList);
        }
        List<FormConfigTreeVO> titles = JSONUtil.toList(titlesStr, FormConfigTreeVO.class);
        List<String> indexList = titles.stream().map(FormConfigTreeVO::getDataIndex).collect(Collectors.toList());
        return resultList.stream().map(map -> InventoryAttachmentJsExcel.builder()
                .name(MapUtil.getStr(map, "200"))
//                .comment(MapUtil.getStr(map, indexList.get(2)))
                .threatType(MapUtil.getStr(map, indexList.get(2)))
                .process(MapUtil.getStr(map, indexList.get(3)))
                .level(MapUtil.getStr(map, indexList.get(4)))
                .t(MapUtil.getStr(map, indexList.get(5)))
                .v(MapUtil.getStr(map, indexList.get(6)))
                .b(MapUtil.getStr(map, indexList.get(7)))
                .l(MapUtil.getStr(map, indexList.get(8)))
                .f(MapUtil.getStr(map, indexList.get(9)))
                .rn(MapUtil.getStr(map, indexList.get(10)))
                .riskLevel(MapUtil.getStr(map, indexList.get(11)))
                .build()).collect(Collectors.toList());
    }

    @Override
    public ReportTypeEnum getType() {
        return ReportTypeEnum.JIANGSU;
    }
}
