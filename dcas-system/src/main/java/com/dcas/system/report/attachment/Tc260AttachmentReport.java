package com.dcas.system.report.attachment;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.resource.ClassPathResource;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcelFactory;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.dcas.common.annotation.SchemaSwitch;
import com.dcas.common.domain.entity.CoModelAnalysisResult;
import com.dcas.common.mapper.CoModelAnalysisResultMapper;
import com.dcas.common.model.dto.ExportWordDto;
import com.dcas.common.model.excel.InventoryAttachmentTc260Excel;
import com.dcas.common.model.vo.FormConfigTreeVO;
import com.dcas.common.model.vo.QueryProjectOperationExportVo;
import com.dcas.system.report.ReportTypeEnum;
import com.dcas.system.report.ReportUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.OutputStream;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 江苏专供资产清单
 *
 * <AUTHOR>
 * @date 2024/01/19 16:45
 **/
@RequiredArgsConstructor
@Component
@Slf4j
public class Tc260AttachmentReport implements AttachmentReportInterface {


    private final CoModelAnalysisResultMapper coModelAnalysisResultMapper;

    @Value("${safety.profile}")
    private String basePath;

    @SchemaSwitch(ExportWordDto.class)
    @Override
    public String exportWord(ExportWordDto dto, QueryProjectOperationExportVo vo) throws Exception {

        String path = String.join(File.separator, basePath, "temp", "附件：数据资产清单.xlsx");
        FileUtil.touch(path);

        //输出结果
        OutputStream out = Files.newOutputStream(Paths.get(path));
        // 附件 资产列表
        //查询资产盘点表
        List<InventoryAttachmentTc260Excel> result = getAssetRisk(dto.getOperationId());
        EasyExcelFactory.write(out)
            .withTemplate(new ClassPathResource("classpath://template/InventoryAttachmentTc260Template.xlsx").getStream())
            .sheet().doWrite(result);
        return path;
    }

    @Override
    public void exportWord(HttpServletResponse response, ExportWordDto dto, QueryProjectOperationExportVo vo)
        throws Exception {
        String path = exportWord(dto, vo);
        ReportUtil.output(response, path);
    }

    private List<InventoryAttachmentTc260Excel> getAssetRisk(String operationId) {

        // 附件 资产列表
        List<CoModelAnalysisResult> coModelAnalysisResultList = coModelAnalysisResultMapper
            .selectList(new QueryWrapper<CoModelAnalysisResult>().eq("operation_id", operationId));

        String titlesStr = "";
        List<Map> resultList = new ArrayList<>();
        for (CoModelAnalysisResult coModelAnalysisResult : coModelAnalysisResultList){
            titlesStr = coModelAnalysisResult.getTitles();
            List<Map> mapList = JSONUtil.toList(coModelAnalysisResult.getResult(), Map.class);
            resultList.addAll(mapList);
        }
        List<FormConfigTreeVO> titles = JSONUtil.toList(titlesStr, FormConfigTreeVO.class);
        Map<String, String> indexColumnMap = new HashMap<>(16);
        titles.forEach(formConfigTreeVO -> {
            if (Boolean.TRUE.equals(formConfigTreeVO.getDataColumn())){
                String title = formConfigTreeVO.getTitle().trim();
                switch (title) {
                    case "所属业务系统":
                        indexColumnMap.put(formConfigTreeVO.getDataIndex(), "busSystemName");
                        break;
                    case "数据类型":
                        indexColumnMap.put(formConfigTreeVO.getDataIndex(), "dataType");
                        break;
                    case "数据级别":
                        indexColumnMap.put(formConfigTreeVO.getDataIndex(), "dataLevel");
                        break;
                    case "风险类型":
                        indexColumnMap.put(formConfigTreeVO.getDataIndex(), "riskType");
                        break;
                    case "风险危害程度":
                        indexColumnMap.put(formConfigTreeVO.getDataIndex(), "riskHazard");
                        break;
                    case "风险发生的可能性":
                        indexColumnMap.put(formConfigTreeVO.getDataIndex(), "riskOccurrence");
                        break;
                    case "风险等级":
                        indexColumnMap.put(formConfigTreeVO.getDataIndex(), "riskLevel");
                        break;
                    default:
                        break;
                }
            } else {
                if (CollUtil.isNotEmpty(formConfigTreeVO.getChildren())) {
                    formConfigTreeVO.getChildren().forEach(child -> {
                        if ("资产名称".equals(child.getTitle())) {
                            indexColumnMap.put(child.getDataIndex(), "assetName");
                        } else if ("敏感等级".equals(child.getTitle())) {
                            indexColumnMap.put(child.getDataIndex(), "sensitiveLevel");
                        }
                    });
                } else {
                    if ("所属业务系统".equals(formConfigTreeVO.getTitle())) {
                        indexColumnMap.put(formConfigTreeVO.getDataIndex(), "busSystemName");
                    }
                }
            }
        });

        Map<String, List<InventoryAttachmentTc260Excel>> busSystemMap = new HashMap<>(16);
        resultList.forEach(map -> {
            Map<String, Object> resultMap = new HashMap<>(16);
            for (Object o : map.keySet()) {
                String key = (String)o;
                resultMap.put(indexColumnMap.get(key), map.get(key));
            }
            String busSystem = (String)resultMap.get("busSystemName");
            if (busSystemMap.containsKey(busSystem)){
                List<InventoryAttachmentTc260Excel> list = busSystemMap.get(busSystem);

                list.add(InventoryAttachmentTc260Excel.builder().busSystem(busSystem).assetName(
                    (String)resultMap.get("assetName")).riskHazard((String)resultMap.get("riskHazard")).riskLevel(
                    (String)resultMap.get("riskLevel")).riskOccurrence((String)resultMap.get("riskOccurrence")).riskType(
                    (String)resultMap.get("riskType")).dataType((String)resultMap.get("dataType")).dataLevel(
                    (String)resultMap.get("dataLevel")).sensitiveLevel( Integer.valueOf((String)resultMap.get("sensitiveLevel"))).build());
            } else {
                List<InventoryAttachmentTc260Excel> list = new ArrayList<>();
                list.add(InventoryAttachmentTc260Excel.builder().busSystem(busSystem).assetName(
                    (String)resultMap.get("assetName")).riskHazard((String)resultMap.get("riskHazard")).riskLevel(
                    (String)resultMap.get("riskLevel")).riskOccurrence((String)resultMap.get("riskOccurrence")).riskType(
                    (String)resultMap.get("riskType")).dataType((String)resultMap.get("dataType")).dataLevel(
                    (String)resultMap.get("dataLevel")).sensitiveLevel(
                    Integer.valueOf((String)resultMap.get("sensitiveLevel"))).build());
                busSystemMap.put(busSystem, list);
            }
        });
        List<InventoryAttachmentTc260Excel> result = new ArrayList<>();
        busSystemMap.forEach((k,v)->result.addAll(v));
        return result;
    }

    @Override
    public ReportTypeEnum getType() {
        return ReportTypeEnum.TC260;
    }

}

