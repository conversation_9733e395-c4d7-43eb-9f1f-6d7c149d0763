package com.dcas.system.report.attachment;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.resource.ClassPathResource;
import cn.hutool.core.map.MapUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcelFactory;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.dcas.common.annotation.SchemaSwitch;
import com.dcas.common.domain.entity.CoModelAnalysisResult;
import com.dcas.common.mapper.CoModelAnalysisResultMapper;
import com.dcas.common.model.dto.ExportWordDto;
import com.dcas.common.model.excel.InventoryAttachmentDraExcel;
import com.dcas.common.model.excel.InventoryAttachmentJrExcel;
import com.dcas.common.model.excel.InventoryAttachmentTc260Excel;
import com.dcas.common.model.vo.FormConfigTreeVO;
import com.dcas.common.model.vo.QueryProjectOperationExportVo;
import com.dcas.system.report.ReportTypeEnum;
import com.dcas.system.report.ReportUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.OutputStream;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 江苏专供资产清单
 *
 * <AUTHOR>
 * @date 2024/01/19 16:45
 **/
@RequiredArgsConstructor
@Component
@Slf4j
public class DraAttachmentReport implements AttachmentReportInterface {


    private final CoModelAnalysisResultMapper coModelAnalysisResultMapper;

    @Value("${safety.profile}")
    private String basePath;

    @SchemaSwitch(ExportWordDto.class)
    @Override
    public String exportWord(ExportWordDto dto, QueryProjectOperationExportVo vo) throws Exception {

        String path = String.join(File.separator, basePath, "temp", "附件：数据资产清单.xlsx");
        FileUtil.touch(path);

        //输出结果
        OutputStream out = Files.newOutputStream(Paths.get(path));
        // 附件 资产列表
        //查询资产盘点表
        List<InventoryAttachmentDraExcel> result = getAssetRisk(dto.getOperationId());
        EasyExcelFactory.write(out)
            .withTemplate(new ClassPathResource("classpath://template/InventoryAttachmentDraTemplate.xlsx").getStream())
            .sheet().doWrite(result);
        return path;
    }

    @Override
    public void exportWord(HttpServletResponse response, ExportWordDto dto, QueryProjectOperationExportVo vo)
        throws Exception {
        String path = exportWord(dto, vo);
        ReportUtil.output(response, path);
    }

    private List<InventoryAttachmentDraExcel> getAssetRisk(String operationId) {
        List<CoModelAnalysisResult> coModelAnalysisResultList = coModelAnalysisResultMapper
            .selectList(new QueryWrapper<CoModelAnalysisResult>().eq("operation_id", operationId));

        String titlesStr = "";
        List<Map> resultList = new ArrayList<>();
        for (CoModelAnalysisResult coModelAnalysisResult : coModelAnalysisResultList){
            titlesStr = coModelAnalysisResult.getTitles();
            List<Map> mapList = JSONUtil.toList(coModelAnalysisResult.getResult(), Map.class);
            resultList.addAll(mapList);
        }
        List<FormConfigTreeVO> titles = JSONUtil.toList(titlesStr, FormConfigTreeVO.class);
        //[{"title":"所属业务系统","dataIndex":"0","width":null,"dataColumn":false,"indicatorId":null,"colFormat":null,"children":[]},
        //{"title":"资产信息","dataIndex":"1","width":null,"dataColumn":false,"indicatorId":null,"colFormat":null,"children":[
        //{"title":"资产名称","dataIndex":"200","width":140,"dataColumn":false,"indicatorId":null,"colFormat":null,"children":[]},
        //{"title":"敏感等级","dataIndex":"201","width":140,"dataColumn":false,"indicatorId":null,"colFormat":null,"children":[]}]},
        //{"title":"数据注释","dataIndex":"2","width":140,"dataColumn":true,"indicatorId":369,"colFormat":"STRING","children":[]},
        //{"title":"数据级别","dataIndex":"3","width":140,"dataColumn":true,"indicatorId":526,"colFormat":"NUMBER","children":[]},
        //{"title":"风险类型","dataIndex":"4","width":140,"dataColumn":true,"indicatorId":532,"colFormat":"STRING","children":[]},
        //{"title":"风险危害程度","dataIndex":"5","width":null,"dataColumn":false,"indicatorId":null,"colFormat":null,"children":[
        //{"title":"风险危害程度级别","dataIndex":"600","width":140,"dataColumn":true,"indicatorId":535,"colFormat":"STRING","children":[]},
        //{"title":"风险危害程度等级","dataIndex":"601","width":140,"dataColumn":true,"indicatorId":534,"colFormat":"STRING","children":[]}]},
        //{"title":"风险发生的可能性","dataIndex":"6","width":null,"dataColumn":false,"indicatorId":null,"colFormat":null,"children":[
        //{"title":"风险发生可能性级别","dataIndex":"700","width":140,"dataColumn":true,"indicatorId":537,"colFormat":"STRING","children":[]},
        //{"title":"风险发生可能性等级","dataIndex":"701","width":140,"dataColumn":true,"indicatorId":536,"colFormat":"STRING","children":[]}]},
        //{"title":"风险评价","dataIndex":"7","width":null,"dataColumn":false,"indicatorId":null,"colFormat":null,"children":[
        //{"title":"风险级别","dataIndex":"800","width":140,"dataColumn":true,"indicatorId":588,"colFormat":"STRING","children":[]},
        //{"title":"风险等级","dataIndex":"801","width":140,"dataColumn":true,"indicatorId":540,"colFormat":"STRING","children":[]}]}]
        AtomicInteger sortedIndex = new AtomicInteger(1);
        return resultList.stream().map(map -> InventoryAttachmentDraExcel.builder()
            .sort(sortedIndex.getAndIncrement())
            .busSystem(MapUtil.getStr(map, "0"))
            .assetName(MapUtil.getStr(map, "200"))
            .sensitiveLevel(MapUtil.getStr(map, "201"))
            .dataType(MapUtil.getStr(map, "2"))
            .dataLevel(MapUtil.getStr(map, "3"))
            .riskType(MapUtil.getStr(map, "4"))
            .harmLevel(MapUtil.getStr(map, "600"))
            .harmGrade(MapUtil.getStr(map, "601"))
            .possibilityLevel(MapUtil.getStr(map, "700"))
            .possibilityGrade(MapUtil.getStr(map, "701"))
            .evaluateLevel(MapUtil.getStr(map, "800"))
            .evaluateGrade(MapUtil.getStr(map, "801"))
            .build()).collect(Collectors.toList());
    }

    @Override
    public ReportTypeEnum getType() {
        return ReportTypeEnum.DRA;
    }

}

