package com.dcas.system.report.attachment;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.resource.ClassPathResource;
import cn.hutool.core.map.MapUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcelFactory;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.dcas.common.annotation.SchemaSwitch;
import com.dcas.common.domain.entity.*;
import com.dcas.common.enums.ColFormatEnum;
import com.dcas.common.enums.LabelEnum;
import com.dcas.common.mapper.*;
import com.dcas.common.model.dto.ExportWordDto;
import com.dcas.common.model.excel.InventoryAttachmentDefaultExcel;
import com.dcas.common.model.excel.InventoryAttachmentDraExcel;
import com.dcas.common.model.vo.FormConfigTreeVO;
import com.dcas.common.model.vo.QueryProjectOperationExportVo;
import com.dcas.common.model.vo.ReportAssetRiskVO;
import com.dcas.common.utils.DcasUtil;
import com.dcas.system.report.ReportTypeEnum;
import com.dcas.system.report.ReportUtil;
import com.google.common.collect.Maps;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.OutputStream;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 资产清单附件报告
 *
 * <AUTHOR>
 * @date 2024/01/08 20:02
 **/
@RequiredArgsConstructor
@Component
public class DefaultAttachmentReport implements AttachmentReportInterface {

    private final CoOperationMapper coOperationMapper;
    private final CoModelAnalysisResultMapper coModelAnalysisResultMapper;
    private final IndicatorResultMapper indicatorResultMapper;
    private final FormConfigMapper formConfigMapper;
    private final CoConstantMapper coConstantMapper;

    @Value("${safety.profile}")
    private String basePath;

    @SchemaSwitch(ExportWordDto.class)
    @Override
    public String exportWord(ExportWordDto dto, QueryProjectOperationExportVo vo) throws Exception {

        String path = String.join(File.separator, basePath, "temp", "附件：数据资产清单.xlsx");
        FileUtil.touch(path);

        //输出结果
        OutputStream out = Files.newOutputStream(Paths.get(path));
        // 附件 资产列表
        //查询资产盘点表
        List<InventoryAttachmentDefaultExcel> result = getAssetRisk(dto.getOperationId());
        EasyExcelFactory.write(out)
            .withTemplate(new ClassPathResource("classpath://template/InventoryAttachmentDefaultTemplate.xlsx").getStream())
            .sheet().doWrite(result);
        return path;
    }

    @Override
    public void exportWord(HttpServletResponse response, ExportWordDto dto, QueryProjectOperationExportVo vo)
        throws Exception {
        String path  = exportWord(dto, vo);
        ReportUtil.output(response, path);
    }

    private List<InventoryAttachmentDefaultExcel> getAssetRisk(String operationId) {
        List<CoModelAnalysisResult> coModelAnalysisResultList = coModelAnalysisResultMapper
            .selectList(new QueryWrapper<CoModelAnalysisResult>().eq("operation_id", operationId));

        List<Map> resultList = new ArrayList<>();
        for (CoModelAnalysisResult coModelAnalysisResult : coModelAnalysisResultList){
            List<Map> mapList = JSONUtil.toList(coModelAnalysisResult.getResult(), Map.class);
            resultList.addAll(mapList);
        }
        //[{"title":"所属业务系统","dataIndex":"0","dataColumn":false,"children":[]},
        //{"title":"资产信息","dataIndex":"1","dataColumn":false,"children":[
        //{"title":"资产名称","dataIndex":"200","width":140,"dataColumn":false,"children":[]},
        //{"title":"敏感等级","dataIndex":"201","width":140,"dataColumn":false,"children":[]}]},
        //{"title":"数据采集安全风险","dataIndex":"2","dataColumn":false,"children":[
        //{"title":"风险系数","dataIndex":"300","width":140,"dataColumn":true,"indicatorId":277,"colFormat":"PERCENTAGE","children":[]},
        //{"title":"风险值","dataIndex":"301","width":140,"dataColumn":true,"indicatorId":577,"colFormat":"NUMBER","children":[]},
        //{"title":"风险等级","dataIndex":"302","width":140,"dataColumn":true,"indicatorId":284,"colFormat":"STRING","children":[]}]},
        //{"title":"数据传输安全风险","dataIndex":"3","dataColumn":false,"children":[
        //{"title":"风险系数","dataIndex":"400","width":140,"dataColumn":true,"indicatorId":278,"colFormat":"PERCENTAGE","children":[]},
        //{"title":"风险值","dataIndex":"401","width":140,"dataColumn":true,"indicatorId":578,"colFormat":"NUMBER","children":[]},
        //{"title":"风险等级","dataIndex":"402","width":140,"dataColumn":true,"indicatorId":285,"colFormat":"STRING","children":[]}]},
        //{"title":"数据存储安全风险","dataIndex":"4","dataColumn":false,"children":[
        //{"title":"风险系数","dataIndex":"500","width":140,"dataColumn":true,"indicatorId":279,"colFormat":"PERCENTAGE","children":[]},
        //{"title":"风险值","dataIndex":"501","width":140,"dataColumn":true,"indicatorId":579,"colFormat":"NUMBER","children":[]},
        //{"title":"风险等级","dataIndex":"502","width":140,"dataColumn":true,"indicatorId":286,"colFormat":"STRING","children":[]}]},
        //{"title":"数据处理安全风险","dataIndex":"5","dataColumn":false,"children":[
        //{"title":"风险系数","dataIndex":"600","width":140,"dataColumn":true,"indicatorId":280,"colFormat":"PERCENTAGE","children":[]},
        //{"title":"风险值","dataIndex":"601","width":140,"dataColumn":true,"indicatorId":580,"colFormat":"NUMBER","children":[]},
        //{"title":"风险等级","dataIndex":"602","width":140,"dataColumn":true,"indicatorId":287,"colFormat":"STRING","children":[]}]},
        //{"title":"数据交换安全风险","dataIndex":"6","dataColumn":false,"children":[
        //{"title":"风险系数","dataIndex":"700","width":140,"dataColumn":true,"indicatorId":281,"colFormat":"PERCENTAGE","children":[]},
        //{"title":"风险值","dataIndex":"701","width":140,"dataColumn":true,"indicatorId":581,"colFormat":"NUMBER","children":[]},
        //{"title":"风险等级","dataIndex":"702","width":140,"dataColumn":true,"indicatorId":288,"colFormat":"STRING","children":[]}]},
        //{"title":"数据销毁安全风险","dataIndex":"7","dataColumn":false,"children":[
        //{"title":"风险系数","dataIndex":"800","width":140,"dataColumn":true,"indicatorId":282,"colFormat":"PERCENTAGE","children":[]},
        //{"title":"风险值","dataIndex":"801","width":140,"dataColumn":true,"indicatorId":582,"colFormat":"NUMBER","children":[]},
        //{"title":"风险等级","dataIndex":"802","width":140,"dataColumn":true,"indicatorId":289,"colFormat":"STRING","children":[]}]},
        //{"title":"通用安全风险","dataIndex":"8","dataColumn":false,"children":[
        //{"title":"风险系数","dataIndex":"900","width":140,"dataColumn":true,"indicatorId":283,"colFormat":"PERCENTAGE","children":[]},
        //{"title":"风险值","dataIndex":"901","width":140,"dataColumn":true,"indicatorId":583,"colFormat":"NUMBER","children":[]},
        //{"title":"风险等级","dataIndex":"902","width":140,"dataColumn":true,"indicatorId":290,"colFormat":"STRING","children":[]}]},
        //{"title":"综合风险等级","dataIndex":"9","dataColumn":false,"children":[
        //{"title":"综合风险等级","dataIndex":"1000","width":140,"dataColumn":true,"indicatorId":585,"colFormat":"NUMBER","children":[]}]}]
        return resultList.stream().map(map -> InventoryAttachmentDefaultExcel.builder()
            .busSystem(MapUtil.getStr(map, "0"))
            .dataAsset(MapUtil.getStr(map, "200"))
            .sensitiveLevel(MapUtil.getStr(map, "201"))
            .dataCollegeRiskFactor(MapUtil.getStr(map, "300"))
            .dataCollegeRiskValue(MapUtil.getStr(map, "301"))
            .dataCollegeRiskLevel(MapUtil.getStr(map, "302"))
            .dataTransRiskFactor(MapUtil.getStr(map, "400"))
            .dataTransRiskValue(MapUtil.getStr(map, "401"))
            .dataTransRiskLevel(MapUtil.getStr(map, "402"))
            .dataStorageRiskFactor(MapUtil.getStr(map, "500"))
            .dataStorageRiskValue(MapUtil.getStr(map, "501"))
            .dataStorageRiskLevel(MapUtil.getStr(map, "502"))
            .dataProcessRiskFactor(MapUtil.getStr(map, "600"))
            .dataProcessRiskValue(MapUtil.getStr(map, "601"))
            .dataProcessRiskLevel(MapUtil.getStr(map, "602"))
            .dataChangeRiskFactor(MapUtil.getStr(map, "700"))
            .dataChangeRiskValue(MapUtil.getStr(map, "701"))
            .dataChangeRiskLevel(MapUtil.getStr(map, "702"))
            .dataDisposeRiskFactor(MapUtil.getStr(map, "800"))
            .dataDisposeRiskValue(MapUtil.getStr(map, "801"))
            .dataDisposeRiskLevel(MapUtil.getStr(map, "802"))
            .generalRiskFactor(MapUtil.getStr(map, "900"))
            .generalRiskValue(MapUtil.getStr(map, "901"))
            .generalRiskLevel(MapUtil.getStr(map, "902"))
            .riskLevel(MapUtil.getStr(map, "1000"))
            .build()).collect(Collectors.toList());
    }

    @Override
    public ReportTypeEnum getType() {
        return ReportTypeEnum.DEFAULT;
    }

}
