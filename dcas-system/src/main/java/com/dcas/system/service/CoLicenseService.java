package com.dcas.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dcas.common.core.domain.RequestModel;
import com.dcas.common.domain.entity.CoLicense;
import com.dcas.common.model.vo.LoadLicenseVo;
import com.dcas.common.model.vo.QueryLicenseVo;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.Map;

/**
 * license服务层
 *
 * <AUTHOR>
 * @Date 2022/11/18 18:04
 * @ClassName CoLicenseService
 */
public interface CoLicenseService extends IService<CoLicense> {
    /**
     * 查询激活状态
     * @Date 2022/11/18 18:05
     * @param lncSerialId  激活码
     * @return * @return String
     */
    QueryLicenseVo queryLicense(String lncSerialId);

    /**
     * 查询本机识别码
     *
     * @return 机器码
     */
    String queryMachineCode();

    /**
     * 查询许可证表
     * @Date 2022/11/18 18:05
     * @param dto  激活码
     * @return * @return CoLicense
     */
    LoadLicenseVo queryLicenseResult(RequestModel<CoLicense> dto);

    /**
     * 上传license.lsc
     *
     * @param file request
     * @return * @return Map<Object>
     * @throws IOException e
     * @Date 2022/6/6 10:16
     */
    Map<String, Object> uploadFile(MultipartFile file) throws IOException;

    /**
     * 加载license文件
     * @param type 文件类型
     * @param path 文件路径
     * @return
     */
    LoadLicenseVo loadLicense(Integer type, String path);
    /**
     * 加载license文件
     * @param deviceId 设备ID
     * @return
     */
    void doSendTerminalHearth(String deviceId);
}
