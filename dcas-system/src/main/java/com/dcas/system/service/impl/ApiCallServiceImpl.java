package com.dcas.system.service.impl;

import cn.hutool.core.lang.TypeReference;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONUtil;
import com.dcas.common.domain.entity.ApiInterface;
import com.dcas.common.domain.entity.TaskStep;
import com.dcas.common.exception.ServiceException;
import com.dcas.common.model.other.InParam;
import com.dcas.common.model.vo.IntegrationFormFieldVO;
import com.dcas.common.utils.SecurityUtils;
import com.dcas.common.utils.StringUtils;
import com.dcas.system.service.IApiCallService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;


/**
 * API调用Service业务层处理（占位符实现）
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class ApiCallServiceImpl implements IApiCallService {

    /**
     * 执行API调用
     * 
     * @param taskStep 任务步骤配置
     * @param params 输入数据
     * @return API调用结果
     */
    @Override
    public ApiCallResult executeApiCall(TaskStep taskStep, List<IntegrationFormFieldVO> params) {
        log.info("执行API调用，步骤ID: {}, 端点: {}, 方法: {}", 
                taskStep.getId(), taskStep.getApiEndpoint(), taskStep.getHttpMethod());
        
        long startTime = System.currentTimeMillis();
        // 字符串转对象
        ObjectMapper mapper = new ObjectMapper();
        try {
            // 验证API配置
            if (!validateApiConfig(taskStep)) {
                return ApiCallResult.failure(null, null, "API配置验证失败", 
                        System.currentTimeMillis() - startTime);
            }
            List<IntegrationFormFieldVO> headParams = JSONUtil.toList(taskStep.getRequestHeaders(), IntegrationFormFieldVO.class);
            InParam inParam = mapper.readValue(taskStep.getRequestParams(), InParam.class);
            // 构建请求头
            Map<String, String> headers = new HashMap<>();
            headParams.forEach(param -> {
                if (StringUtils.isNotBlank(param.getValue())) {
                    // 解密请求头参数
                    headers.put(param.getName(), SecurityUtils.decryptAes(param.getValue()));
                }
            });
            log.debug("请求头: {}", headers);

            // 根据接口方法类型处理请求
            String apiMethod = taskStep.getHttpMethod().toUpperCase();
            String apiPath = taskStep.getApiEndpoint();

            switch (apiMethod) {
                case "GET":
                    try (HttpResponse response = executeGetRequest(apiPath, headers, inParam, params)) {
                        if (response.getStatus() >= 200 && response.getStatus() < 300) {
                            return ApiCallResult.success(response.getStatus(), response.body(), null, System.currentTimeMillis() - startTime);
                        } else {
                            return ApiCallResult.failure(response.getStatus(), response.body(), null, System.currentTimeMillis() - startTime);
                        }
                    }
                case "POST":
                    try (HttpResponse response = executePostRequest(apiPath, headers, inParam, params)) {
                        if (response.getStatus() >= 200 && response.getStatus() < 300) {
                            return ApiCallResult.success(response.getStatus(), response.body(), null, System.currentTimeMillis() - startTime);
                        } else {
                            return ApiCallResult.failure(response.getStatus(), response.body(), null, System.currentTimeMillis() - startTime);
                        }
                    }
                case "PUT":
                    try (HttpResponse response = executePutRequest(apiPath, headers, inParam, params)) {
                        if (response.getStatus() >= 200 && response.getStatus() < 300) {
                            return ApiCallResult.success(response.getStatus(), response.body(), null, System.currentTimeMillis() - startTime);
                        } else {
                            return ApiCallResult.failure(response.getStatus(), response.body(), null, System.currentTimeMillis() - startTime);
                        }
                    }
                default:
                    throw new ServiceException("<UNK>API<UNK>");
            }
        } catch (Exception e) {
            long executionTime = System.currentTimeMillis() - startTime;
            log.error("API调用异常，步骤ID: {}", taskStep.getId(), e);
            return ApiCallResult.exception("API调用异常: " + e.getMessage(), executionTime);
        }
    }

    @Override
    public boolean testApiConnection(ApiInterface apiInterface, List<IntegrationFormFieldVO> configParams, String url) {
        log.info("测试API连接，端点: {}, 方法: {}", url + apiInterface.getApiPath(), apiInterface.getApiMethod());

        // 字符串转对象
        ObjectMapper mapper = new ObjectMapper();
        try {
            InParam inParam = mapper.readValue(apiInterface.getReqParams(), InParam.class);
            // 构建请求头
            Map<String, String> headers = new HashMap<>();

            for (IntegrationFormFieldVO configParam : configParams) {
                // 解密请求头参数
                headers.put(configParam.getName(), SecurityUtils.decryptAes(configParam.getValue()));
            }
            log.debug("请求头: {}", headers);

            // 根据接口方法类型处理请求
            String apiMethod = apiInterface.getApiMethod().toUpperCase();
            String apiPath = url + apiInterface.getApiPath();

            // 提取当前接口入参参数
            List<IntegrationFormFieldVO> interfaceInParams = configParams.stream().filter(
                    c -> Objects.equals(c.getParamType(), 1)).collect(Collectors.toList());

            switch (apiMethod) {
                case "GET":
                    try (HttpResponse httpResponse = executeGetRequest(apiPath, headers, inParam, interfaceInParams)) {
                        return httpResponse.getStatus() >= 200 && httpResponse.getStatus() < 300;
                    }
                case "POST":
                    try (HttpResponse httpResponse = executePostRequest(apiPath, headers, inParam, interfaceInParams)) {
                        return httpResponse.getStatus() >= 200 && httpResponse.getStatus() < 300;
                    }
                case "PUT":
                    try (HttpResponse httpResponse = executePutRequest(apiPath, headers, inParam, interfaceInParams)) {
                        return httpResponse.getStatus() >= 200 && httpResponse.getStatus() < 300;
                    }
                default:
                    log.error("不支持的API方法类型: {}", apiMethod);
                    return false;
            }
        } catch (Exception e) {
            log.error("测试API连接失败", e);
            return false;
        }
    }

    /**
     * 执行GET请求
     */
    private HttpResponse executeGetRequest(String apiPath, Map<String, String> headers, InParam inParam, List<IntegrationFormFieldVO> configParams) throws ServiceException {
        try {
            // 构建查询参数
            Map<String, Object> queryParams = buildParamsMap(inParam, configParams, 1);
            log.debug("GET请求参数: {}", queryParams);

            // 使用Hutool的HttpUtil发送GET请求
            HttpResponse response = HttpRequest.get(apiPath)
                    .headerMap(headers, true)  // 添加请求头
                    .form(queryParams)         // 添加表单参数
                    .timeout(10000)            // 设置超时时间为10秒
                    .execute();

            int statusCode = response.getStatus();
            log.info("GET请求响应状态码: {}", statusCode);

            return response;
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    /**
     * 执行POST请求
     */
    private HttpResponse executePostRequest(String apiPath, Map<String, String> headers, InParam inParam, List<IntegrationFormFieldVO> configParams) throws Exception {
        try {
            // 构建请求体
            String requestBody = buildRequestBody(inParam, configParams);
            log.debug("POST请求体: {}", requestBody);

            // 使用Hutool的HttpUtil发送POST请求
            HttpResponse response = HttpRequest.post(apiPath)
                    .headerMap(headers, true)           // 添加请求头
                    .body(requestBody)                  // 设置JSON请求体
                    .contentType("application/json")    // 设置Content-Type
                    .timeout(10000)                     // 设置超时时间为10秒
                    .execute();

            int statusCode = response.getStatus();
            log.info("POST请求响应状态码: {}, 响应体: {}", statusCode, response.body());

            return response;
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    /**
     * 执行PUT请求
     */
    private HttpResponse executePutRequest(String apiPath, Map<String, String> headers, InParam inParam, List<IntegrationFormFieldVO> configParams) throws ServiceException {
        try {
            // 构建请求体
            String requestBody = buildRequestBody(inParam, configParams);
            log.debug("PUT请求体: {}", requestBody);

            // 使用Hutool的HttpUtil发送PUT请求
            HttpResponse response = HttpRequest.put(apiPath)
                    .headerMap(headers, true)           // 添加请求头
                    .body(requestBody)                  // 设置JSON请求体
                    .contentType("application/json")    // 设置Content-Type
                    .timeout(10000)                     // 设置超时时间为10秒
                    .execute();

            int statusCode = response.getStatus();
            log.info("PUT请求响应状态码: {}, 响应体: {}", statusCode, response.body());

            return response;
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    /**
     * 构建请求体JSON
     */
    private String buildRequestBody(InParam inParam, List<IntegrationFormFieldVO> configParams) throws JsonProcessingException {
        ObjectMapper mapper = new ObjectMapper();

        // 使用递归方式构建JSON结构
        Map<String, Object> rootObject = new HashMap<>();
        buildNestedStructure(rootObject, inParam.getColumnList(), configParams, 1);

        return mapper.writeValueAsString(rootObject);
    }

    /**
     * 递归构建嵌套的JSON结构
     */
    private void buildNestedStructure(Map<String, Object> currentObject, List<InParam.ColumnParam> columnList,
                                      List<IntegrationFormFieldVO> configParams, int currentLevel) {
        buildNestedStructure(currentObject, columnList, configParams, currentLevel, "");
    }

    /**
     * 递归构建嵌套的JSON结构（支持location定位）
     */
    private void buildNestedStructure(Map<String, Object> currentObject, List<InParam.ColumnParam> columnList,
                                      List<IntegrationFormFieldVO> configParams, int currentLevel, String currentLocation) {
        // 过滤当前层级和当前location的字段
        List<InParam.ColumnParam> currentLevelColumns = columnList.stream()
                .filter(col -> col.getLevel() == currentLevel && isMatchingLocation(col.getLocation(), currentLocation))
                .collect(Collectors.toList());

        for (InParam.ColumnParam column : currentLevelColumns) {
            String columnName = column.getColumnName();

            // 根据字段类型处理
            if ("object".equalsIgnoreCase(column.getColumnType())) {
                // 对象类型，创建新的嵌套对象
                Map<String, Object> nestedObject = new HashMap<>();
                currentObject.put(columnName, nestedObject);

                // 构建下一级的location路径
                String nextLocation = buildNextLocation(currentLocation, columnName);

                // 递归处理下一级
                buildNestedStructure(nestedObject, columnList, configParams, currentLevel + 1, nextLocation);
            } else if ("array".equalsIgnoreCase(column.getColumnType()) || "list".equalsIgnoreCase(column.getColumnType())) {
                // 数组类型，创建列表
                List<Object> arrayList = new ArrayList<>();
                currentObject.put(columnName, arrayList);

                // 简化处理，假设数组只有一个元素模板
                Map<String, Object> arrayItem = new HashMap<>();
                arrayList.add(arrayItem);

                // 构建下一级的location路径
                String nextLocation = buildNextLocation(currentLocation, columnName);

                // 递归处理数组内的对象
                buildNestedStructure(arrayItem, columnList, configParams, currentLevel + 1, nextLocation);
            } else {
                // 基本类型，从configParams获取值 类型3为coc配置默认值
                String value = column.getType() == 0 ? column.getValue() : getValueFromConfigParams(columnName, configParams, currentLocation);

                // 根据字段类型转换值
                Object typedValue = convertValueByType(value, column.getColumnType());
                currentObject.put(columnName, typedValue);
            }
        }
    }

    /**
     * 判断字段的location是否匹配当前location
     */
    private boolean isMatchingLocation(String fieldLocation, String currentLocation) {
        // 如果字段location为空或者为"/"，表示第一层
        if (StringUtils.isBlank(fieldLocation) || "/".equals(fieldLocation)) {
            return StringUtils.isBlank(currentLocation);
        }

        // 去除开头和结尾的"/"
        String normalizedFieldLocation = normalizeLocation(fieldLocation);
        String normalizedCurrentLocation = normalizeLocation(currentLocation);

        return normalizedFieldLocation.equals(normalizedCurrentLocation);
    }

    /**
     * 标准化location路径，去除开头和结尾的"/"
     */
    private String normalizeLocation(String location) {
        if (StringUtils.isBlank(location)) {
            return "";
        }

        String normalized = location.trim();
        if (normalized.startsWith("/")) {
            normalized = normalized.substring(1);
        }
        if (normalized.endsWith("/")) {
            normalized = normalized.substring(0, normalized.length() - 1);
        }

        return normalized;
    }

    /**
     * 构建下一级的location路径
     */
    private String buildNextLocation(String currentLocation, String parentName) {
        if (StringUtils.isBlank(currentLocation)) {
            return parentName;
        }
        return currentLocation + "/" + parentName;
    }

    /**
     * 从configParams中获取字段值
     */
    private String getValueFromConfigParams(String columnName, List<IntegrationFormFieldVO> configParams) {
        return getValueFromConfigParams(columnName, configParams, "");
    }

    /**
     * 从configParams中获取字段值（支持location匹配）
     */
    private String getValueFromConfigParams(String columnName, List<IntegrationFormFieldVO> configParams, String currentLocation) {
        return configParams.stream()
                .filter(param -> param.getName().equals(columnName) && isMatchingLocation(param.getLocation(), currentLocation))
                .findFirst()
                .map(IntegrationFormFieldVO::getValue)
                .orElse("");
    }

    /**
     * 根据字段类型转换值
     */
    private Object convertValueByType(String value, String columnType) {
        if (StringUtils.isBlank(value)) {
            return null;
        }

        switch (columnType.toLowerCase()) {
            case "integer":
            case "int":
                return Integer.parseInt(value);
            case "long":
                return Long.parseLong(value);
            case "double":
            case "float":
                return Double.parseDouble(value);
            case "boolean":
                return Boolean.parseBoolean(value);
            default:
                return value;
        }
    }

    /**
     * 构建平铺的参数Map (用于GET请求)
     */
    private Map<String, Object> buildParamsMap(InParam inParam, List<IntegrationFormFieldVO> configParams, int level) {
        Map<String, Object> result = new HashMap<>();

        // 获取所有参数
        for (InParam.ColumnParam column : inParam.getColumnList()) {
            if (column.getLevel() == level) {
                String value = column.getType() == 0 ? column.getValue() : getValueFromConfigParams(column.getColumnName(), configParams);
                if (StringUtils.isNotBlank(value)) {
                    // 转换为适当的类型
                    result.put(column.getColumnName(), convertValueByType(value, column.getColumnType()));
                }
            }
        }

        return result;
    }

    /**
     * 验证API配置
     * 
     * @param taskStep 任务步骤配置
     * @return 验证结果
     */
    @Override
    public boolean validateApiConfig(TaskStep taskStep) {
        if (taskStep == null) {
            log.error("任务步骤配置为空");
            return false;
        }
        
        if (StringUtils.isEmpty(taskStep.getApiEndpoint())) {
            log.error("API端点为空，步骤ID: {}", taskStep.getId());
            return false;
        }
        
        if (StringUtils.isEmpty(taskStep.getHttpMethod())) {
            log.error("HTTP方法为空，步骤ID: {}", taskStep.getId());
            return false;
        }
        
        // 验证HTTP方法
        String method = taskStep.getHttpMethod().toUpperCase();
        if (!method.matches("GET|POST|PUT|DELETE")) {
            log.error("不支持的HTTP方法: {}, 步骤ID: {}", method, taskStep.getId());
            return false;
        }
        
        // 验证URL格式
        String endpoint = taskStep.getApiEndpoint();
        if (!endpoint.startsWith("http://") && !endpoint.startsWith("https://")) {
            log.error("无效的API端点格式: {}, 步骤ID: {}", endpoint, taskStep.getId());
            return false;
        }
        
        return true;
    }

    /**
     * 解析响应数据
     * 
     * @param responseData 响应数据
     * @param taskStep 任务步骤配置
     * @return 解析后的输出数据
     */
    @Override
    public String parseResponseData(String responseData, TaskStep taskStep) {
        if (StringUtils.isEmpty(responseData)) {
            return "";
        }
        
        // TODO: 实现响应数据解析逻辑
        // 这里是占位符实现，简单返回响应数据
        log.debug("解析响应数据，步骤ID: {}, 响应长度: {}", 
                taskStep.getId(), responseData.length());
        
        return responseData;
    }

    /**
     * 检查响应是否成功
     * 
     * @param responseStatus 响应状态码
     * @param responseData 响应数据
     * @return 是否成功
     */
    @Override
    public boolean isResponseSuccessful(Integer responseStatus, String responseData) {
        if (responseStatus == null) {
            return false;
        }
        
        // HTTP 2xx 状态码表示成功
        return responseStatus >= 200 && responseStatus < 300;
    }

    /**
     * 模拟API响应（占位符方法）
     */
    private String simulateApiResponse(TaskStep taskStep, String inputData) {
        // 根据不同的API端点模拟不同的响应
        String endpoint = taskStep.getApiEndpoint();
        
        if (endpoint.contains("user")) {
            return "{\"status\":\"success\",\"data\":{\"userId\":123,\"userName\":\"testUser\"},\"message\":\"用户信息获取成功\"}";
        } else if (endpoint.contains("order")) {
            return "{\"status\":\"success\",\"data\":{\"orderId\":456,\"orderStatus\":\"completed\"},\"message\":\"订单处理成功\"}";
        } else if (endpoint.contains("payment")) {
            return "{\"status\":\"success\",\"data\":{\"paymentId\":789,\"amount\":100.00},\"message\":\"支付处理成功\"}";
        } else {
            return "{\"status\":\"success\",\"data\":{\"result\":\"操作成功\"},\"message\":\"API调用成功\"}";
        }
    }
}
