package com.dcas.system.service.impl;

import cn.hutool.core.util.StrUtil;
import com.dcas.common.enums.CommonResultCode;
import com.dcas.common.enums.SysConfigEnum;
import com.dcas.common.enums.SysEditionEnum;
import com.dcas.common.exception.ServiceException;
import com.dcas.common.model.dto.VersionInfoDTO;
import com.dcas.common.domain.entity.LibrarySyncHistory;
import com.dcas.common.domain.entity.SysConfig;
import com.dcas.common.mapper.LibrarySyncHistoryMapper;
import com.dcas.system.service.LibraryUpdateCheckService;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mchz.dcas.client.DcasCocClient;
import com.mchz.dcas.client.model.response.LibrarySyncResponse;
import com.mchz.dcas.client.model.response.TerminalControlResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.Objects;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date 2023/5/12 14:39
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class LibraryUpdateCheckServiceImpl implements LibraryUpdateCheckService {

    private final DcasCocClient dcasCocClient;
    private final SysConfigServiceImpl sysConfigService;
    private final LibrarySyncHistoryMapper librarySyncHistoryMapper;

    @Override
    public Boolean checkVersion() {
        SysConfig sysConfig = sysConfigService.selectConfigById(11L);
        LibrarySyncResponse response = dcasCocClient.queryVersionV2(sysConfig.getConfigValue());
        String version = response.getVersion();
        if (StrUtil.isEmpty(version))
            return false;
        LibrarySyncHistory syncHistory = librarySyncHistoryMapper.selectLastedHistory();
        // 首次同步 || 版本号不一致  需要新增一条数据
        if (Objects.isNull(syncHistory) || !Objects.equals(version, syncHistory.getVersion())) {
            LibrarySyncHistory insert = LibrarySyncHistory.builder()
                    .tag(response.getTag())
                    .version(version)
                    .sync(Boolean.FALSE)
                    .build();
            librarySyncHistoryMapper.insert(insert);
            return true;
        }
        // 版本号一致，检查是否同步
        return !syncHistory.getSync();
    }

    @Override
    public TerminalControlResponse checkSystemVersion() {
        VersionInfoDTO versionInfoDTO = null;
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            File jsonFile = new File("/etc/dcas/version.json");
            versionInfoDTO = objectMapper.readValue(jsonFile, VersionInfoDTO.class);
        } catch (Exception e) {
            log.error("读取版本信息失败", e);
        }
        if (Objects.isNull(versionInfoDTO) || StrUtil.isEmpty(versionInfoDTO.getCURRENT_VERSION()))
            throw new ServiceException("读取版本信息失败");
        return dcasCocClient.queryTerminalVersion(versionInfoDTO.getCURRENT_VERSION());
    }
}
