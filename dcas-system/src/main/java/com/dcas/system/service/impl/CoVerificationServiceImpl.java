package com.dcas.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.resource.ClassPathResource;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dcas.common.annotation.DataValidator;
import com.dcas.common.annotation.SchemaSwitch;
import com.dcas.common.core.domain.RequestModel;
import com.dcas.common.enums.*;
import com.dcas.common.exception.ServiceException;
import com.dcas.common.exception.params.FailParamsException;
import com.dcas.common.model.req.AdviseGenerateReq;
import com.dcas.common.utils.*;
import com.dcas.common.utils.bean.BeanUtils;
import com.dcas.common.utils.judge.JudgeResultUtil;
import com.dcas.common.utils.params.CheckUtil;
import com.dcas.common.model.dto.*;
import com.dcas.common.domain.entity.*;
import com.dcas.common.model.param.AnalysisParam;
import com.dcas.common.model.param.VerificationParam;
import com.dcas.common.model.req.IdsReq;
import com.dcas.common.model.req.VerificationReq;
import com.dcas.common.model.vo.QueryModelVo;
import com.dcas.common.model.vo.VerificationExcel;
import com.dcas.common.model.vo.VerificationVO;
import com.dcas.common.mapper.*;
import com.dcas.system.service.*;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.mchz.dcas.client.enums.FileAnalysisType;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * fetch data
 *
 * <AUTHOR>
 * @Date 2022/6/20 12:22
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CoVerificationServiceImpl extends ServiceImpl<CoVerificationMapper, CoVerification> implements CoVerificationService {

    private final CoFileMapper coFileMapper;
    private final CoProgressMapper coProgressMapper;
    private final CoProcessTreeMapper coProcessTreeMapper;
    private final CoOperationMapper coOperationMapper;
    private final CoBaseAssessMapper coBaseAssessMapper;
    private final CoVerificationMapper coVerificationMapper;
    private final QuestionnaireContentMapper questionnaireContentMapper;

    private final AdviseService adviseService;
    private final AdviseItemMapper adviseItemMapper;
    private final CoFileService coFileService;
    private final ModelAnalysisService modelAnalysisService;
    private final AnalysisTemplateMapper analysisTemplateMapper;
    private final QuestionnaireContentService questionnaireContentService;
    private final DynamicProcessTreeServiceImpl dynamicProcessTreeService;
    private final TagMapper tagMapper;
    private final StandardMapper standardMapper;
    private final SpecialEvaluationConfigMapper specialEvaluationConfigMapper;
    private final SpecCalcResultMapper specCalcResultMapper;
    private final FileAnalysisLogicMapper fileAnalysisLogicMapper;

    /*
     * 业务逻辑：
     * 1.选择模型：co_model表levels字段等于评估基础数据库的level字段
     * 应用：
     * ---以评估基础库为主，最多576
     * ---1.根据入场operationId、labelId查出问卷记录
     * ---2.取出bpcodes，将bpcodes转成list，以问卷id为唯一键区分的
     * ---3.评估基础库对比每一条问卷id的bpcode,符合记1分，部分符合0.5分，不符合0分，不适用排除
     * ---4.如果一个问卷id对应的有多个bpcode,全都符合记1分，有一个部分符合则记0.5分，全都不符合0分，全都不适用排除
     */

    @DataValidator(type = TemplateTypeEnum.ANALYSIS_TEMPLATE)
    @Override
    @SchemaSwitch(value = String.class)
    public List<QueryModelVo> listModel(String operationId) {
        CoOperation coOperation = coOperationMapper.selectById(operationId);
        List<String> industryList = StrUtil.split(coOperation.getRelatedIndustry(), StrUtil.COMMA);
        Map<String, String> industryMap = tagMapper.selectList(new QueryWrapper<Tag>().eq("type_id", 2)
            .eq("status", 0)).stream().collect(Collectors.toMap(t -> t.getId().toString(), Tag::getName));
        List<AnalysisModelDTO> analysisModels = new ArrayList<>();
        if (Boolean.TRUE.equals(coOperation.getIsSpec())){
            analysisModels = analysisTemplateMapper.getModelBySpecId(coOperation.getSpecId());
        } else {
            analysisModels = analysisTemplateMapper.selectModel();
            // 获取所有专项关联能力分析模板id
            List<Integer> allRelTemplateIds = specialEvaluationConfigMapper.listRelTemplateIds(null);
            // 按行业 地域过滤
            analysisModels = analysisModels.stream().filter(
                a -> CoOperationServiceImpl.templateFilter(coOperation.getRelatedDistrict(), industryList,
                    StrUtil.EMPTY, a.getRegionCode(), a.getIndustryCode(), industryMap) && !allRelTemplateIds.contains(
                    Integer.valueOf(a.getId()))).collect(Collectors.toList());

        }
        Map<String, List<AnalysisModelDTO>> modelMap =
            analysisModels.stream().collect(Collectors.groupingBy(AnalysisModelDTO::getKey));
        String modelName = coVerificationMapper.queryApplyModel(operationId);

        return modelMap.entrySet().stream().map(model -> {
            QueryModelVo vo = new QueryModelVo();
            String[] keyStr = model.getKey().split(StrUtil.COMMA);
            vo.setModelId(Integer.valueOf(keyStr[0]));
            vo.setModelName(keyStr[1]);
            vo.setLevelList(model.getValue().stream()
                .filter(m -> m.getLevel() != null && StrUtil.isNotEmpty(LevelEnum.getLevelByCode(m.getLevel() + "")))
                .map(m -> LevelEnum.getLevelByCode(m.getLevel() + "")).sorted().distinct()
                .collect(Collectors.toList()));
            vo.setTypeList(model.getValue().stream().map(AnalysisModelDTO::getDimension).distinct().collect(Collectors.toList()));
            Optional<String> optional =  model.getValue().stream().map(AnalysisModelDTO::getRegulatoryFactor)
                .filter(Objects::nonNull).distinct().findFirst();
            optional.ifPresent(vo::setRegulatoryFactorScope);
            vo.setSelected(!StrUtil.isEmpty(modelName) && modelName.equals(keyStr[1]));
            vo.setUnderstand(model.getValue().stream().map(AnalysisModelDTO::getUnderstand).distinct().findFirst().orElse(false));
            return vo;
        }).collect(Collectors.toList());
    }

    /**
     * 获取能力级别
     *
     * @param list request
     * @return * @return List<String>
     * @Date 2022/9/6 11:28
     */
    public List<String> getLevelList(List<String> list) {
        List<String> levelList = new ArrayList<>();
        list.forEach(p -> {
            String level = LevelEnum.getLevelByCode(p);
            if (StrUtil.isNotEmpty(level)) {
                levelList.add(level);
            }
        });
        return levelList;
    }

    /**
     * 获取视图类型
     *
     * @param modelName request
     * @return * @return List<String>
     * @Date 2022/9/6 11:28
     */
    @Override
    public List<String> getTypeList(String modelName) {
        List<String> list = new ArrayList<>();
        if ("综合模型".equals(modelName)) {
            list.add("管理能力");
            list.add("技术能力");
        } else if ("DSMM模型".equals(modelName)) {
            list.add("人员能力");
            list.add("制度流程");
            list.add("组织建设");
            list.add("技术工具");
        }
        return list;
    }

    /**
     * 应用
     *
     * @param param request
     * @return * @return List<QueryVerificationVo>
     * @Date 2022/6/17 10:30
     */
    @Override
    @SchemaSwitch(value = QueryVerificationDto.class)
    public PageInfo<CoVerification> query(QueryVerificationDto param, Integer pageNum, Integer pageSize) {
        //入参校验
        CheckUtil.checkParams(param);
        CoOperation coOperation = coOperationMapper.selectById(param.getOperationId());
        QueryWrapper<CoVerification> query = new QueryWrapper<>();
        query.eq("operation_id", param.getOperationId());
        query.eq("label_id", param.getLabelId());
        query.eq("model_name", param.getModelName());

        // 专项评估判断 需求：选择3级，列表分析内容展示3级与以下等级，其他等级同上；
        List<String> levelList = new ArrayList<>();
        if (StrUtil.isNotEmpty(param.getLevel())) {
            String level = LevelEnum.getCodeByLevel(param.getLevel());
            if (level != null){
                if (coOperation.getIsSpec() != null && coOperation.getIsSpec()){
                    levelList.addAll(LevelEnum.getUnderLevelByLevel(level));
                } else {
                    levelList.add(level);
                }
                query.in("level", levelList);
            }
        }
        if (StrUtil.isNotEmpty(param.getGpDimension())) {
            query.like("gp_dimension", param.getGpDimension());
        }
        if (StrUtil.isNotEmpty(param.getBpCode())) {
            query.like("bp_code", param.getBpCode());
        }
        if (!Objects.equals("全部", param.getResult()) && StrUtil.isNotEmpty(param.getResult())) {
            query.eq("result", param.getResult());
        }
        if (StrUtil.isNotEmpty(param.getDescription())){
            query.and(wrapper -> wrapper.like("description", param.getDescription()).or().like("incompatible", param.getDescription()));
        }
        if (StrUtil.isNotEmpty(param.getStandardProvision())){
            query.like("standard_provision", param.getStandardProvision());
        }
        query.orderByAsc("standard_id").orderByAsc("bp_code");
        AnalysisTemplate analysisTemplate = analysisTemplateMapper.selectById(param.getModelId());
        List<Standard> standardList = standardMapper.queryStandardFile(Long.valueOf(param.getModelId()), param.getStandardFile(),
            analysisTemplate.getNoDisplay());
        Map<Integer, String> standardKeyValueMap = new HashMap<>(16);
        if (CollUtil.isNotEmpty(standardList)){
            standardKeyValueMap = standardList.stream().collect(Collectors.toMap(Standard::getId,Standard::getName));
        }
        if (StrUtil.isNotEmpty(param.getStandardFile())) {
            query.in("standard_id", standardKeyValueMap.keySet());
        }
        if (StrUtil.isNotEmpty(param.getSystemResult())) {
            query.like("system_result", param.getSystemResult());
        }
        query.in("standard_id", standardKeyValueMap.keySet());
        try (Page<?> ignore = PageMethod.startPage(pageNum, pageSize)) {
            List<CoVerification> coVerifications = coVerificationMapper.selectList(query);
            if (CollUtil.isNotEmpty(coVerifications)){
                Map<Integer, String> finalStandardKeyValueMap = standardKeyValueMap;
                coVerifications.forEach(coVerification -> coVerification.setStandardFile(finalStandardKeyValueMap.get(coVerification.getStandardId())));
            }
            return new PageInfo<>(coVerifications);
        }

    }

    @Transactional(rollbackFor = Throwable.class)
    @Override
    public void apply(QueryVerificationDto dto) {
        CoOperation coOperation = coOperationMapper.selectById(dto.getOperationId());
        // 查询问卷调研模块下所有子类标签
        QueryWrapper<DynamicProcessTree> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("operation_id", dto.getOperationId());
        queryWrapper.eq("parent_id", LabelEnum.WJDY.getCode());
        List<DynamicProcessTree> treeList = dynamicProcessTreeService.getBaseMapper().selectList(queryWrapper);
        Set<Long> labels = treeList.stream().map(DynamicProcessTree::getTreeId).collect(Collectors.toSet());
        int count = coProgressMapper.selectFinishedLabelSize(dto.getOperationId(), labels);
        if (count != labels.size()) {
            throw new ServiceException("请先完成问卷调研模块所有内容再进行操作");
        }
        // 专项评估判断 需求：选择3级，列表分析内容展示3级与以下等级，其他等级同上；
        List<Integer> levelList = new ArrayList<>();
        String level = LevelEnum.getCodeByLevel(dto.getLevel());
        if (level != null){
            if (coOperation.getIsSpec() != null && coOperation.getIsSpec()){
                levelList.addAll(LevelEnum.getUnderLevelCodeByLevel(level));
            } else {
                levelList.add(Integer.parseInt(level));
            }
        }

        List<CoVerification> coVerifications = modelAnalysisService.abilityAnalysis(
                AnalysisParam.builder()
                        .operationId(dto.getOperationId())
                        .labelId(LabelEnum.NLFX.getCode())
                        .levelList(levelList)
                        .templateId(dto.getModelId())
                        .isSpec(coOperation.getIsSpec())
                        .specId(coOperation.getSpecId())
                        .build());
        this.saveBatch(coVerifications);

        // 如果不是第一次应用，则需要设置处置建议需要提示框
        Integer itemCount = adviseItemMapper.selectCount(new QueryWrapper<AdviseItem>()
                .eq("operation_id", dto.getOperationId()).eq("type", AdviceTypeEnum.CAPACITY.getCode().toString()));
        if (itemCount > 0) {
            UpdateWrapper<CoOperation> updateWrapper = new UpdateWrapper<>();
            updateWrapper.set("repeat", 1);
            updateWrapper.eq("operation_id", dto.getOperationId());
            coOperationMapper.update(new CoOperation(), updateWrapper);
        }

        adviseService.generate(new AdviseGenerateReq(dto.getOperationId(), 2));
    }

    @Override
    public Integer isCompleted(String operationId) {
        List<Integer> integers = coVerificationMapper.queryVerificationLevel(operationId);
        if (CollUtil.isEmpty(integers)) {
            return null;
        }
        return integers.size() > 1 ? 3 : integers.get(0);
    }


    /**
     * 保存更新现状验证
     * @param addDto request
     * @param coQuestionnaireList request
     * @return * @return int
     * @Date 2022/6/20 11:32
     */
    @Transactional(rollbackFor = Throwable.class)
    @Override
    public Boolean saveVerification(AddVerificationDTO addDto, List<CoQuestionnaire> coQuestionnaireList) throws Exception {

        /**
         * ---以评估基础库为主，最多576
         * ---.评估基础库对比每一条问卷id的bpcode,只有一个bpcode时，符合记1分，部分符合0.5分，不符合0分，不适用排除
         * ---.如果一个问卷id对应的有多个bpcode,全都符合记1分，有一个部分符合则记0.5分，全都不符合0分，全都不适用排除
         *  注：先要判断在评估基础库中存在，然后才去根据选项算分
         *  注：是以评估基础库的bpcode为基础去计算符合不符合的
         *  注：存储时=>评估数据库id=现状核验id
         */
        //TODO 并发问题
        QueryVerificationDto retrieveDto = new QueryVerificationDto();
        BeanUtils.copyProperties(addDto, retrieveDto);

        boolean flag = false;
        int row = 0;
        for (ModelEnum modelEnum : ModelEnum.values()) {
            //模板名称
            retrieveDto.setModelName(modelEnum.getName());

            List<CoVerification> unInsertList = new ArrayList<>();
            List<CoVerification> unUpdateList = new ArrayList<>();

            //分类
            List<String> typeList = ModelLabEnum.getLabByModelName(modelEnum.getName());

            //能力级别
            List<String> levelList = addDto.getLevelList();
            typeList.stream().forEach(t -> {
                //分类对应的Gp维度
                List<String> gpDimensionList = new ArrayList<>();
                if (ModelEnum.ZHMB.getName().equals(modelEnum.getName())){
                    gpDimensionList = TypeGpDimensionEnum.getGpDimensionByName(t);
                }else if (ModelEnum.DSMMMB.getName().equals(modelEnum.getName())){
                    gpDimensionList.add(t);
                }

                //待新增
                List<CoQuestionnaire> questionnaireList1 = new ArrayList<>();
                //待更新问卷列表
                List<CoQuestionnaire> questionnaireList2 = new ArrayList<>();

                List<String> finalGpDimensionList = gpDimensionList;
                levelList.stream().forEach(l -> {
                    QueryWrapper<CoVerification> queryWrapper = new QueryWrapper<>();
                    queryWrapper.eq("operation_id", retrieveDto.getOperationId());
                    queryWrapper.eq("model_name", retrieveDto.getModelName());
                    queryWrapper.in("gp_dimension", finalGpDimensionList); //type=gp维度，多分级之后还要增加level条件
                    //按作业id、模板名称、gp维度筛选出来的已存表数据
                    List<CoVerification> savedList = coVerificationMapper.selectList(queryWrapper);

                    //已存表bpCode 和入参code对比
                    if (savedList.size() >0) {
                        savedList.stream().forEach(s -> {
                            //问卷入参列表
                            coQuestionnaireList.stream().forEach(q -> {
                                if (StringUtils.isNotBlank(q.getBpCodes())) {
                                    List<String> bpCodeList = Arrays.asList(q.getBpCodes().split(","));
                                    if (bpCodeList.contains(s.getBpCode())) {
                                        CoQuestionnaire coQuestionnaire = new CoQuestionnaire(s.getBpCode(), q.getOpt(), q.getRemark());
                                        questionnaireList2.add(coQuestionnaire);
                                    }
                                }
                            });
                        });
                    }

                    //查询评估基础库
                    QueryWrapper<CoBaseAssess> query = new QueryWrapper<>();
                    query.eq("level",l);
                    query.in("gp_dimension", finalGpDimensionList);
                    List<CoBaseAssess> coBaseAssessList = coBaseAssessMapper.selectList(query);
                    List<String> collect = coBaseAssessList.stream().map(h -> h.getBpCode()).collect(Collectors.toList());
                    //筛选出入参bpCode列表
                    List<String> bpCodeList = new ArrayList<>();
                    coQuestionnaireList.stream().filter(b -> StringUtils.isNotBlank(b.getBpCodes())).forEach(b -> {
                        bpCodeList.addAll(Arrays.asList(b.getBpCodes().split(",")));
                    });
                    //入参bpCode与当前按等级、gp维度筛选出的评估库的交集-已存表bpCode
                    List<String> intersection = collect.stream().filter(item -> bpCodeList.contains(item)).collect(Collectors.toList());
                    //当前按等级、gp维度筛选出的已存bpCode
                    List<String> savedBpCodeList = savedList.stream().map(p -> p.getBpCode()).collect(Collectors.toList());
                    //待新增bpCode = 入参bpCode与按等级、gp维度筛选出的评估库的交集-已存表bpCode
                    intersection.removeAll(savedBpCodeList);
                    coQuestionnaireList.stream().forEach(d -> {
                        if (StringUtils.isNotBlank(d.getBpCodes())){
                        List<String> bpCodes = Arrays.asList(d.getBpCodes().split(","));
                        intersection.stream().forEach(f -> {
                            if (bpCodes.contains(f)){
                                CoQuestionnaire coQuestionnaire = new CoQuestionnaire(f, d.getOpt(), d.getRemark());
                                questionnaireList1.add(coQuestionnaire);
                            }
                        });
                        }
                    });
                    retrieveDto.setLevel(l);
                });
                //获取评估比对结果
                if (questionnaireList2.size() > 0){
                    unUpdateList.addAll(this.getResultList(retrieveDto, questionnaireList2));
                }

                if (questionnaireList1.size() > 0){
                    unInsertList.addAll(this.getResultList(retrieveDto, questionnaireList1));
                }
            });
            flag = this.saveBatch(unInsertList);

            //更新
            for (CoVerification verification : unUpdateList){
                UpdateWrapper<CoVerification> updateWrapper = new UpdateWrapper<>();
                updateWrapper.set("result", verification.getResult());
                updateWrapper.eq("operation_id", verification.getOperationId());
                updateWrapper.eq("model_name",verification.getModelName());
                updateWrapper.eq("gp_dimension", verification.getGpDimension());
                updateWrapper.eq("bp_code", verification.getBpCode());
                row = row + coVerificationMapper.update(new CoVerification(), updateWrapper);
            }
        }

        return flag || row >0 ;
    }


    //评估符合情况（questionnaire与cobaseAssess对比结果）
    public List<CoVerification> getResultList(QueryVerificationDto retrieveDto, List<CoQuestionnaire> coQuestionnaires) {

        //查询评估基础库
        List<CoBaseAssess> coBaseAssessList = coBaseAssessMapper.queryBaseAssessList(retrieveDto);

        //包含重复bpCode对应选项的List集合；因为存在不同问题的bpCode可能相同的情况
        List<Map<String, OptRemarkDTO>> parseList = new ArrayList<>();

        //评估基础库遍历 问卷表id对应的bpcode
        coBaseAssessList.stream().forEach(b -> {
            coQuestionnaires.stream().forEach(q -> {
                if (StringUtils.isNotBlank(q.getBpCodes())) {
                    List<String> bpCodes = Arrays.asList(q.getBpCodes().split(","));
                    bpCodes.stream().forEach(m -> {
                        //当评估基础库bpCode和问卷id对应的bpCode相等
                        if (b.getBpCode().equals(m)) {
                            Map<String, OptRemarkDTO> map = new HashMap<>();
                            map.put(b.getBpCode(), OptRemarkDTO.builder().opt(q.getOpt()).remark(q.getRemark()).build());
                            parseList.add(map);
                        }
                    });
                }
            });
        });

        //按bpCode分组，得到不重复的以bpCode为键的Map集合
        Map<Set<String>, List<Map<String, OptRemarkDTO>>> parseMap = parseList.stream().collect(Collectors.groupingBy(p -> p.keySet()));
        Map<String, OptRemarkListDTO> mapList = new HashMap<>();
        for (Map.Entry<Set<String>, List<Map<String, OptRemarkDTO>>> entry : parseMap.entrySet()) {
            String bpCode = StringUtils.join(entry.getKey().toArray());
            //map结构 bpcode/opt; parseMap.get(key):当bpCode对应的选项有多个时，获取List
            List<Map<String, OptRemarkDTO>> map = entry.getValue();

            //当某一条mapList.size>1; 则去组装opts
            if (map.size() > 1) {
                //组装opts
                List<String> optList = new ArrayList<>();
                List<String> remarkList = new ArrayList<>();
                for (Map<String, OptRemarkDTO> stringMap : map) {
                    optList.add(stringMap.get(bpCode).getOpt());
                    remarkList.add(stringMap.get(bpCode).getRemark());
                }
                mapList.put(bpCode, OptRemarkListDTO.builder().opts(optList).remarks(remarkList).build());

            } else {
                List<String> optList = new ArrayList<>();
                optList.add(map.get(0).get(bpCode).getOpt());
                List<String> remarkList = new ArrayList<>();
                remarkList.add(map.get(0).get(bpCode).getRemark());
                mapList.put(bpCode, OptRemarkListDTO.builder().opts(optList).remarks(remarkList).build());
            }
        }

        //查询结果
        List<CoVerification> coVerificationList = new ArrayList<CoVerification>();
        //根据选项列表判断符合情况
        for (Map.Entry<String, OptRemarkListDTO> entry : mapList.entrySet()) {
            //key=bpcode,result=符合情况
            String key = entry.getKey();
            String result = JudgeResultUtil.getResult(entry.getValue().getOpts());
            String description = Strings.join(entry.getValue().getRemarks(), ';');
            //返回值
            coBaseAssessList.stream().forEach(p -> {
                if (p.getBpCode().equals(key)) {
                    CoVerification coVerification = new CoVerification();
                    BeanUtils.copyProperties(p, coVerification);
                    BeanUtils.copyProperties(retrieveDto, coVerification);
                    coVerification.setLabelId(LabelEnum.NLFX.getCode());
                    coVerification.setResult(result);
                    coVerification.setDescription(description);
                    coVerificationList.add(coVerification);
                }
            });
        }

        return coVerificationList;
    }


    /**
     * 修改/设为不适用
     *
     * @param dto request
     * @return * @return int
     * @Date 2022/6/20 11:32
     */
    @Transactional(rollbackFor = Throwable.class)
    @Override
    public Integer edit(RequestModel<UpdateVerificationDto> dto) {

        //判断数据是否存在
        CoVerification verification = coVerificationMapper.selectById(dto.getPrivator().getVerificationId());
        if (ObjectUtils.isEmpty(verification)) {
            throw new FailParamsException("请先保存当前数据");
        }
        CoVerification coVerification = new CoVerification();
        BeanUtils.copyProperties(dto.getPrivator(), coVerification);
        coVerification.setScore(syncScore(coVerification.getResult(), verification.getScore(), dto.getPrivator()));
        return coVerificationMapper.updateById(coVerification);
    }

    private BigDecimal syncScore(String result, BigDecimal score, UpdateVerificationDto dto) {
        BigDecimal newScore;
        if (OptEnum.A.getInfo().equals(result)){
            newScore = BigDecimal.valueOf(OptEnum.A.getScore());
        } else if (OptEnum.B.getInfo().equals(result)) {
            newScore = BigDecimal.valueOf(OptEnum.B.getScore());
        } else if (OptEnum.C.getInfo().equals(result)) {
            newScore = BigDecimal.valueOf(OptEnum.C.getScore());
        }  else if (OptEnum.E.getInfo().equals(result)) {
            newScore = BigDecimal.valueOf(OptEnum.E.getScore());
        }  else if (OptEnum.D.getInfo().equals(result)){
            newScore = BigDecimal.valueOf(OptEnum.D.getScore());
        } else {
            newScore = score;
        }
        // 更新专项bpcode得分
        specCalcResultMapper.updateByBpCodeAndOperationId(dto.getBpCode(), dto.getOperationId(), newScore);
        return newScore;
    }

    /**
     * 清空核验
     *
     * @param dto request
     * @return * @return int
     * @Date 2022/6/20 11:32
     */
    @Transactional(rollbackFor = Throwable.class)
    @Override
    public Integer remove(RequestModel<CommonDto> dto) {
        QueryWrapper<CoVerification> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("operation_id", dto.getPrivator().getOperationId());
        queryWrapper.eq("label_id", dto.getPrivator().getLabelId());
        return coVerificationMapper.delete(queryWrapper);
    }

    @Override
    @SneakyThrows
    @SchemaSwitch(String.class)
    public void export(String operationId, RequestModel<QueryVerificationDto> dto, HttpServletResponse response) {
        dto.setPageNum(1);
        dto.setPageSize(Integer.MAX_VALUE);
        QueryWrapper<CoVerification> query = new QueryWrapper<>();
        query.eq("operation_id", dto.getPrivator().getOperationId());
        query.eq("model_name", dto.getPrivator().getModelName());
        if (StrUtil.isNotEmpty(dto.getPrivator().getLevel())) {
            query.eq("level", LevelEnum.getCodeByLevel(dto.getPrivator().getLevel()));
        }
        query.orderByAsc("bp_code");
        Map<Integer, String> fileMap = standardMapper.selectList(new QueryWrapper<>()).stream().collect(Collectors.toMap(Standard::getId, Standard::getName));
        try (Page<?> page = PageMethod.startPage(dto.getPageNum(), dto.getPageSize())) {
            PageInfo<CoVerification> coVerificationPageInfo = new PageInfo<>(coVerificationMapper.selectList(query));
            List<VerificationExcel> excel = buildExcelData(coVerificationPageInfo.getList(), fileMap);
            Func.export(response, excel, "能力评估");
        }
    }

    @SchemaSwitch(VerificationReq.class)
    @Override
    public PageResult<VerificationVO> pageQuery(VerificationReq req) {
        String operationId = req.getOperationId();
        final Map<String, String> fileMap = coFileMapper.queryFileByOperationId(
            operationId).stream().collect(Collectors.toMap(CoFile::getFileId, CoFile::getOriginalFileName));
        Map<Long, String> businessSystemMap = getBusinessSystemMap(operationId, req.getBusinessSystem());
        try (Page<Object> page = PageHelper.startPage(req.getCurrentPage(), req.getPageSize());) {
            req.setObjectIds(businessSystemMap.keySet());
            List<VerificationDTO> verifications = questionnaireContentMapper.search(req);
            List<VerificationVO> collect = verifications.stream().map(getVerificationDTOVerificationVOFunction(fileMap, businessSystemMap)).collect(Collectors.toList());
            return PageResult.ofPage(page.getTotal(), collect);
        }
    }

    private Map<Long, String> getBusinessSystemMap(String operationId, String businessSystem) {
        QueryWrapper<DynamicProcessTree> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("operation_id", operationId);
        queryWrapper.like(StrUtil.isNotEmpty(businessSystem),"tree_name", businessSystem);
        List<DynamicProcessTree> dynamicProcessTrees = dynamicProcessTreeService.list(queryWrapper);
        if (CollUtil.isEmpty(dynamicProcessTrees)) {
            QueryWrapper<CoProcessTree> queryWrapper1 = new QueryWrapper<>();
            queryWrapper1.eq("status", '0');
            List<CoProcessTree> coProcessTrees = coProcessTreeMapper.selectList(queryWrapper1);
            return coProcessTrees.stream().collect(Collectors.toMap(CoProcessTree::getTreeId, CoProcessTree::getTreeName));
        }
        return dynamicProcessTrees.stream().collect(Collectors.toMap(DynamicProcessTree::getTreeId, DynamicProcessTree::getTreeName));
    }

    @Override
    @SchemaSwitch(VerificationParam.class)
    public void batchVerification(VerificationParam param) {
        String username = SecurityUtils.getUsername();
        Map<Long, VerificationParam.Verification> verificationMap = param.getList().stream().collect(
                Collectors.toMap(VerificationParam.Verification::getId, Function.identity()));
        List<VerificationDetailDTO> verificationDetailList = baseMapper.queryDetailById(verificationMap.keySet());
        List<QuestionnaireContent> contentList = new ArrayList<>();
        for (VerificationDetailDTO verificationDetail : verificationDetailList) {
            VerificationParam.Verification verification = verificationMap.get(verificationDetail.getId());
            QuestionnaireContent content = new QuestionnaireContent();
            content.setId(verificationDetail.getId());
            if (CollUtil.isEmpty(verification.getFileList())) {
                content.setFileIds("");
            } else {
                content.setFileIds(verification.getFileList().stream().map(VerificationVO.File::getFileId).collect(Collectors.joining(StrUtil.COMMA)));
            }
            if (param.getType() == 1) {
                // 符合 设置符合性现状说明
                content.setExplain(verification.getExplain());
            } else if (param.getType() == 2) {
                // 不符合 如果页面填写则使用页面填写的，否则使用知识库风险项
                content.setExplain(StrUtil.isNotEmpty(verification.getExplain()) ? verification.getExplain() : verificationDetail.getDescribe());
            } else if (param.getType() == 3) {
                content.setInapplicableReason(verification.getExplain());
            }
            content.setVerifier(username);
            contentList.add(content);
        }
        PartitionUtils.part(contentList, questionnaireContentService::updateList);
    }

    @Override
    public Boolean canCommit(String operationId) {
        return questionnaireContentMapper.selectUnVerifyCount(operationId) == 0;
    }

    @Override
    public void batchDelete(IdsReq req) {
        List<QuestionnaireContent> contentList = req.getIds().stream().map(id ->
                QuestionnaireContent.builder().id(id.longValue()).checked(Boolean.FALSE).build()).collect(Collectors.toList());
        questionnaireContentService.updateList(contentList);
    }

    @Override
    public List<VerificationVO> search(VerificationReq req) {
        final Map<String, String> fileMap = coFileMapper.queryFileByOperationId(
                req.getOperationId()).stream().collect(Collectors.toMap(CoFile::getFileId, CoFile::getOriginalFileName));
        final Map<Long, String> businessSystemMap = getBusinessSystemMap(req.getOperationId(), req.getBusinessSystem());
        List<VerificationDTO> VerificationVOS = questionnaireContentMapper.search(req);
        return VerificationVOS.stream().map(getVerificationDTOVerificationVOFunction(fileMap, businessSystemMap)).collect(Collectors.toList());
    }

    private Function<VerificationDTO, VerificationVO> getVerificationDTOVerificationVOFunction(Map<String, String> fileMap, Map<Long, String> businessSystemMap) {
        return v -> {
            VerificationVO verificationVO = BeanUtil.copyProperties(v, VerificationVO.class);
            if (Objects.nonNull(v.getFileIds())) {
                verificationVO.setFileList(StrUtil.isEmpty(v.getFileIds()) ? null : StrUtil.split(v.getFileIds(),
                        StrUtil.COMMA).stream().map(f -> {
                    VerificationVO.File file = new VerificationVO.File();
                    file.setFileId(f);
                    file.setName(fileMap.get(f));
                    return file;
                }).collect(Collectors.toList()));
            }
            verificationVO.setBusinessSystem(businessSystemMap.get(v.getObjectId()));
            return verificationVO;
        };
    }

    @Override
    public void verificationFileDownload(String operationId, HttpServletResponse response) {
        List<String> fileIds = questionnaireContentMapper.selectFileIdsByOperationId(operationId);
        if (CollUtil.isNotEmpty(fileIds))
            coFileService.fileDownload(fileIds, "现状核验文件压缩包.zip", response);
    }

    private List<VerificationExcel> buildExcelData(List<CoVerification> res, Map<Integer, String> fileMap) {
        return res.stream().map(r ->  {
            StringBuilder sb = new StringBuilder();
            if (StrUtil.isNotEmpty(r.getDescription()))
                sb.append(r.getDescription());
            if (StrUtil.isNotEmpty(r.getIncompatible()))
                sb.append(r.getIncompatible());
            return VerificationExcel.builder()
                    .modeName(r.getModelName())
                    .fileName(fileMap.get(r.getStandardId()))
                    .level(r.getLevel())
                    .bpCode(r.getBpCode())
                    .gpDimension(r.getGpDimension())
                    .description(StrUtil.isEmpty(sb.toString()) ? StrUtil.EMPTY : sb.toString())
                    .standardProvision(r.getStandardProvision())
                    .resultDetail(r.getSystemResult())
                    .result(r.getResult()).build();
        }).collect(Collectors.toList());
    }

    @Override
    public void batchModifyFactor(RegulatoryFactorDTO dto) {
        List<CoVerification> coVerifications = new ArrayList<>();
        dto.getVerificationIds().forEach(id -> {
            CoVerification coVerification = new CoVerification();
            coVerification.setVerificationId(id);
            coVerification.setRegulatoryFactor(dto.getFactor());
            coVerifications.add(coVerification);
        });
        this.saveOrUpdateBatch(coVerifications);
    }

    @Override
    public Long queryApplyTemplate(String operationId) {
        return coVerificationMapper.queryApplyTemplateId(operationId);
    }

    @Override
    @SchemaSwitch(String.class)
    public List<Integer> selectStandardIdByOperationId(String operationId) {
        return baseMapper.selectStandardIdByOperationId(operationId);
    }

    @SchemaSwitch(value = String.class)
    @Override
    public void downloadExcel(String operationId, HttpServletResponse response) throws IOException {
        final Map<String, String> fileMap = coFileMapper.queryFileByOperationId(
            operationId).stream().collect(Collectors.toMap(CoFile::getFileId, CoFile::getOriginalFileName));
        Map<Long, String> businessSystemMap = getBusinessSystemMap(operationId, null);
        VerificationReq req = new VerificationReq();
        req.setOperationId(operationId);
        List<VerificationDTO> verifications = questionnaireContentMapper.search(req);
        List<VerificationVO> collect = verifications.stream().map(getVerificationDTOVerificationVOFunction(fileMap, businessSystemMap)).collect(Collectors.toList());
        AtomicInteger sort = new AtomicInteger(1);
        List<com.dcas.common.model.excel.VerificationExcel> excels = collect.stream().map(vo->{
            com.dcas.common.model.excel.VerificationExcel excel = BeanUtil.copyProperties(vo,
                com.dcas.common.model.excel.VerificationExcel.class);
            excel.setSort(sort.getAndIncrement());
            excel.setFileList(CollUtil.isNotEmpty(vo.getFileList()) ? vo.getFileList().stream().map(
                VerificationVO.File::getName).collect(
                Collectors.joining(StrPool.LF)) : "");
            return excel;
        }).collect(Collectors.toList());
        Func.responseSetting(response,
            String.format("%s-%s.xlsx", "现状核验", DateUtils.getExportDateStr(System.currentTimeMillis())));
        EasyExcelFactory.write(response.getOutputStream()).withTemplate(new ClassPathResource("classpath://template/verificationTemplate.xlsx").getStream()).sheet().doWrite(excels);
    }

    @Override
    @SchemaSwitch(String.class)
    public String queryStatusDesc(String operationId) {
        QueryWrapper<CoVerification> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("distinct standard_id");
        queryWrapper.eq("operation_id", operationId);
        Set<Integer> standardIds = coVerificationMapper.selectList(queryWrapper).stream().map(CoVerification::getStandardId).collect(Collectors.toSet());
        if (CollUtil.isEmpty(standardIds))
            return null;
        List<IntervalDTO> attributes = fileAnalysisLogicMapper.selectList(new QueryWrapper<FileAnalysisLogic>().in("file_id",
                standardIds).eq("file_type", "STANDARD")).stream().filter(f ->
                        f.getHasException() && Objects.equals(f.getExceptionType(), FileAnalysisType.MIDDLE.name()))
                .map(f -> JSON.parseObject(f.getAttributes().toString(), IntervalDTO.class)).collect(Collectors.toList());
        return attributes.stream().map(a -> String.format("【%s】：%s", a.getStatusName(), a.getStatusDesc())).collect(Collectors.joining(StrUtil.LF));
    }
}
