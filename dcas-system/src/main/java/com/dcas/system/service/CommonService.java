package com.dcas.system.service;

import com.dcas.common.core.domain.RequestModel;
import com.dcas.common.exception.file.InvalidExtensionException;
import com.dcas.common.model.dto.PrimaryKeyDTO;
import com.dcas.common.domain.entity.Tag;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * fetch data
 *
 * <AUTHOR>
 * @Date 2022/6/6 10:13
 * @ClassName CommonService
 */
public interface CommonService {
    /**
     * 通用上传请求（单个）
     *
     * @param file request
     * @return * @return Map<Object>
     * @throws IOException e
     * @Date 2022/6/6 10:16
     */
    Map<String, Object> uploadFile(MultipartFile file, String operationId) throws IOException, InvalidExtensionException;

    Map<String, Object> uploadImage(MultipartFile file) throws IOException, InvalidExtensionException;

    /**
     * 清理附件：删除表数据，删除服务器存储附件文件
     *
     * @param dto request
     * @return * @return int
     * @Date 2022/7/13 10:19
     */
    int deleteFile(RequestModel<PrimaryKeyDTO> dto);

    List<Tag> listIndustry();
}
