package com.dcas.system.service.impl;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.ZipUtil;
import cn.hutool.http.HttpUtil;
import com.dcas.common.config.SafetyConfig;
import com.dcas.system.config.SyncProperties;
import com.dcas.common.constant.CacheConstants;
import com.dcas.common.core.redis.RedisCache;
import com.dcas.common.enums.SysConfigEnum;
import com.dcas.common.enums.SysEditionEnum;
import com.dcas.common.exception.ServiceException;
import com.dcas.common.domain.entity.LibrarySyncHistory;
import com.dcas.common.domain.entity.SysConfig;
import com.dcas.common.model.vo.LibraryHistoryVO;
import com.dcas.common.mapper.LibrarySyncHistoryMapper;
import com.dcas.system.service.ExecuteScriptService;
import com.dcas.system.service.ISysConfigService;
import com.dcas.system.service.LibrarySyncService;
import com.dcas.system.service.LibraryTemplateConfigService;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Objects;
import java.util.concurrent.*;
import java.util.concurrent.locks.ReentrantLock;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date 2023/5/11 14:53
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class LibrarySyncServiceImpl implements LibrarySyncService {

    private final ReentrantLock mainLock = new ReentrantLock();
    private static final Integer CORE_THREADS = Runtime.getRuntime().availableProcessors() + 1;

    private final RedisCache redisCache;
    private final SyncProperties properties;
    private final ISysConfigService sysConfigService;
    private final ExecuteScriptService executeScriptService;
    private final LibrarySyncHistoryMapper librarySyncHistoryMapper;
    private final LibraryTemplateConfigService libraryTemplateConfigService;

    @Override
    public LibraryHistoryVO needSync() {
        LibraryHistoryVO vo = new LibraryHistoryVO();
        // 首次查询并且没有调用获取最新版本号接口，默认不需要更新
        vo.setUpgrade(Boolean.FALSE);
        // 最新已更新的版本号
        LibrarySyncHistory syncHistory1 = librarySyncHistoryMapper.selectHistoryBySync(Boolean.TRUE);
        // 最新的版本号
        LibrarySyncHistory syncHistory2 = librarySyncHistoryMapper.selectLastedHistory();
        if (Objects.nonNull(syncHistory1)) {
            vo.setVersion(syncHistory1.getVersion());
            vo.setCreateTime(syncHistory1.getCreateTime());
        } else {
            vo.setLastedVersion(Objects.isNull(syncHistory2) ? null : syncHistory2.getVersion());
            // syncHistory1为空 即没有同步过，如果没有最新版本号，不需要更新
            vo.setUpgrade(Objects.isNull(vo.getLastedVersion()) ? Boolean.FALSE : Boolean.TRUE);
            return vo;
        }
        // 是否需要更新 存在最新未更新的版本号
        if (Objects.nonNull(syncHistory2)) {
            vo.setUpgrade(!Objects.equals(syncHistory1.getVersion(), syncHistory2.getVersion()));
            vo.setLastedVersion(syncHistory2.getVersion());
        }
        return vo;
    }

    @Override
    public void sync() {
        final ReentrantLock mainLock = this.mainLock;
        mainLock.lock();
        ExecutorService executorService = new ThreadPoolExecutor(
                CORE_THREADS,
                CORE_THREADS,
                0,
                TimeUnit.MILLISECONDS,
                new LinkedBlockingQueue<>(100),
                new ThreadFactoryBuilder().setNameFormat("library-async-name-%d").setDaemon(true).build());
        try {
            SysConfig sysConfig = sysConfigService.selectConfigById(8L);
            if (Objects.isNull(sysConfig) || Objects.equals(sysConfig.getConfigValue(), "1"))
                throw new ServiceException("知识库同步需要进入维护模式");
            // 定时任务会每周获取最新知识库版本，并设置为未同步，取最新未同步的版本号
            LibraryHistoryVO libraryHistoryVO = needSync();
            if (!libraryHistoryVO.getUpgrade()) {
                log.info("知识库无需同步");
                return;
            }
            String dumpFilePath = properties.getDumpPath() + StrUtil.SLASH + String.format("contentLibrary-%s.sql", libraryHistoryVO.getLastedVersion());
            final String dumpUrl = properties.getHost() + dumpFilePath;
            FileDownloader dumpLoader = new FileDownloader(dumpUrl, SafetyConfig.getDumpPath());
            CompletableFuture<Void> future1 = CompletableFuture.runAsync(dumpLoader, executorService)
                    .thenRunAsync(() -> executeScriptService.executeSqlScript(dumpFilePath), executorService)
                    .exceptionally(ex -> {
                        throw new ServiceException(String.format("知识库同步失败，msg:%s", ex.getMessage()));
                    });

            final String sqlUrl = properties.getHost() + properties.getDumpPath() + StrUtil.SLASH + "sql.zip";
            FileDownloader sqlLoader = new FileDownloader(sqlUrl, SafetyConfig.getSqlPath());
            CompletableFuture<Void> future2 = CompletableFuture.runAsync(sqlLoader, executorService)
                    .thenRunAsync(() -> {
                        String zipPath = SafetyConfig.getSqlPath();
                        String filePath = zipPath + StrUtil.SLASH + "sql.zip";
                        ZipUtil.unzip(filePath, zipPath);
                        FileUtil.del(filePath);
                    })
                    .exceptionally(ex -> {
                        log.error("权限查询脚本文件夹同步失败，msg:{}", ex.getMessage());
                        return null;
                    });

            final String imageUrl = properties.getHost() + properties.getDumpPath() + StrUtil.SLASH + "images.zip";
            FileDownloader imageLoader = new FileDownloader(imageUrl, SafetyConfig.getImagesPath());
            CompletableFuture<Void> future3 = CompletableFuture.runAsync(imageLoader, executorService)
                    .thenRunAsync(() -> {
                        String zipPath = SafetyConfig.getImagesPath();
                        String filePath = zipPath + StrUtil.SLASH + "images.zip";
                        ZipUtil.unzip(filePath, zipPath);
                        FileUtil.del(filePath);
                    })
                    .exceptionally(ex -> {
                        log.error("图片文件夹同步失败，msg:{}", ex.getMessage());
                        return null;
                    });

            final String templateUrl = properties.getHost() + properties.getDumpPath() + StrUtil.SLASH + "templates.zip";
            FileDownloader templateLoader = new FileDownloader(templateUrl, SafetyConfig.getTemplatePath());
            CompletableFuture<Void> future4 = CompletableFuture.runAsync(templateLoader, executorService)
                .thenRunAsync(() -> {
                    String zipPath = SafetyConfig.getTemplatePath();
                    String filePath = zipPath + StrUtil.SLASH + "templates.zip";
                    ZipUtil.unzip(filePath, zipPath);
                    FileUtil.del(filePath);
                })
                .exceptionally(ex -> {
                    log.error("风险模型文件夹同步失败，msg:{}", ex.getMessage());
                    return null;
                });

            CompletableFuture<Void> future = CompletableFuture.allOf(future1, future2, future3, future4);
            future.join();
            redisCache.deleteObject(CacheConstants.ITEM_FILE_KEY);
        } finally {
            // 退出维护模式
            quit();
            executorService.shutdown();
            mainLock.unlock();
        }
    }

    private void quit() {
        SysConfig config = new SysConfig();
        config.setConfigId(8L);
        config.setConfigValue("1");
        sysConfigService.updateConfig(config);
        // 离线更新完知识库，同步知识库模板最新状态
        libraryTemplateConfigService.sync();
    }

    @Data
    @AllArgsConstructor
    static class FileDownloader implements Runnable {

        private String url;
        private String destPath;

        @Override
        public void run() {
            log.debug("开始下载文件，url:{}", url);
            FileUtil.mkdir(destPath);
            HttpUtil.downloadFile(url, destPath);
        }
    }

}
