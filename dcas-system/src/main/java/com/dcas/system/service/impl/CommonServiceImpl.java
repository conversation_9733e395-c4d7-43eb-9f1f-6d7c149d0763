package com.dcas.system.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.dcas.common.algorithms.SnowFlake;
import com.dcas.common.annotation.SchemaSwitch;
import com.dcas.common.config.SafetyConfig;
import com.dcas.common.core.domain.RequestModel;
import com.dcas.common.enums.CommonResultCode;
import com.dcas.common.enums.SysConfigEnum;
import com.dcas.common.enums.SysEditionEnum;
import com.dcas.common.exception.ServiceException;
import com.dcas.common.exception.file.InvalidExtensionException;
import com.dcas.common.utils.ServletUtils;
import com.dcas.common.utils.file.FileUploadUtils;
import com.dcas.common.utils.file.FileUtils;
import com.dcas.common.utils.params.CheckUtil;
import com.dcas.common.model.dto.PrimaryKeyDTO;
import com.dcas.common.domain.entity.CoFile;
import com.dcas.common.domain.entity.Tag;
import com.dcas.common.mapper.CoFileMapper;
import com.dcas.system.service.CommonService;
import com.dcas.system.service.ISysConfigService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * fetch data
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CommonServiceImpl implements CommonService {

    private final CoFileMapper coFileMapper;
    private final TagServiceImpl tagService;
    private final ISysConfigService sysConfigService;

    /**
     * 通用上传请求（单个）
     *
     * @param file request
     * @return * @return Map<Object>
     * @Date 2022/6/6 10:16
     */
    @Override
    public Map<String, Object> uploadFile(MultipartFile file, String operationId) throws IOException, InvalidExtensionException {
        // 上传文件路径
        String uploadPath = SafetyConfig.getUploadPath();
        // 上传并返回新文件路径
        String filePath = FileUploadUtils.upload(uploadPath, file);
        String url = ServletUtils.getUrl() + filePath;

        Map<String, Object> result = new HashMap<>();
        String id = SnowFlake.getId();
        result.put("fileId", id);
        result.put("url", url);
        result.put("filePath", filePath);
        result.put("originalFilename", file.getOriginalFilename());
        result.put("newFileName", FileUtils.getName(filePath));

        //存储到文件表
        CoFile coFile = new CoFile();
        coFile.setFileId(id);
        coFile.setUrl(url);
        coFile.setFilePath(filePath);
        coFile.setOriginalFileName(file.getOriginalFilename());
        coFile.setNewFileName(FileUtils.getName(filePath));
        coFile.setOperationId(operationId);
        coFileMapper.add(coFile);

        return result;

    }

    @Override
    public Map<String, Object> uploadImage(MultipartFile file) throws IOException, InvalidExtensionException {
        // 上传文件路径
        String uploadPath = SafetyConfig.getImagesPath();
        // 上传并返回新文件路径
        String filePath = FileUploadUtils.upload(uploadPath, file);
        String url = ServletUtils.getUrl() + filePath;

        Map<String, Object> result = new HashMap<>();
        String id = SnowFlake.getId();
        result.put("fileId", id);
        result.put("url", url);
        result.put("filePath", filePath);
        result.put("originalFilename", file.getOriginalFilename());
        result.put("newFileName", FileUtils.getName(filePath));

        //存储到文件表
        CoFile coFile = new CoFile();
        coFile.setFileId(id);
        coFile.setUrl(url);
        coFile.setFilePath(filePath);
        coFile.setOriginalFileName(file.getOriginalFilename());
        coFile.setNewFileName(FileUtils.getName(filePath));
        coFileMapper.add(coFile);

        return result;
    }

    /**
     * 清理附件：删除表数据，删除服务器存储附件文件
     *
     * @param dto request
     * @return * @return int
     * @Date 2022/7/13 10:20
     */
    @Transactional(rollbackFor = Throwable.class)
    @Override
    public int deleteFile(RequestModel<PrimaryKeyDTO> dto) {
        //入参校验
        CheckUtil.checkParams(dto.getPrivator());
        CoFile coFile = coFileMapper.selectById(dto.getPrivator().getId());
        String filePath = coFile.getFilePath();
        String newFilePath = filePath.replace("/profile", "./data/storage");

        //删除文件
        File file = new File(newFilePath);
        if (!file.isFile()) {
            log.info("file not found");
            return 0;
        }
        file.delete();

        return coFileMapper.deleteById(dto.getPrivator().getId());
    }

    @Override
    @SchemaSwitch
    public List<Tag> listIndustry() {
        return tagService.list(new QueryWrapper<Tag>().eq("type_id", 2).eq("status", 0))
                .stream().sorted(Comparator.comparing(t -> Objects.equals(t.getName(), "全行业"))).collect(Collectors.toList());
    }
}
