package com.dcas.system.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.http.HttpUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.dcas.common.config.SafetyConfig;
import com.dcas.common.domain.entity.CoLicense;
import com.dcas.common.enums.AttributeEnum;
import com.dcas.system.config.SyncProperties;
import com.dcas.common.enums.SysConfigEnum;
import com.dcas.common.enums.SysEditionEnum;
import com.dcas.common.exception.ServiceException;
import com.dcas.common.utils.file.FileUploadUtils;
import com.dcas.common.model.dto.VersionInfoDTO;
import com.dcas.common.domain.entity.SysConfig;
import com.dcas.common.model.req.SystemUpgradeReq;
import com.dcas.common.model.vo.LibraryHistoryVO;
import com.dcas.system.service.*;
import com.dcas.system.spring.event.SchemaRefreshEvent;
import com.dcas.system.spring.payload.SchemaRefreshPayload;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mchz.dcas.client.model.response.TerminalControlResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.util.Objects;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date 2022/12/30 11:25
 * @since 1.0.1
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SysUpgradeServiceImpl implements ISysUpgradeService {

    private final SyncProperties properties;
    private final ISysConfigService sysConfigService;
    private final LibrarySyncService librarySyncService;
    private final LibraryUpdateCheckService libraryUpdateCheckService;
    private final LibraryTemplateConfigService libraryTemplateConfigService;
    private final CoLicenseService coLicenseService;

    @Override
    public void upgrade(MultipartFile file) {
        SysConfig config = sysConfigService.selectConfigById(SysConfigEnum.MODE.getCode());
        if (Objects.isNull(config) || !Objects.equals(config.getConfigValue(), "0")) {
            throw new ServiceException("仅维护模式支持在线升级");
        }
        String filename = file.getOriginalFilename();
        if (Objects.isNull(filename) || !filename.endsWith(".enc")) {
            throw new ServiceException("不支持的文件格式");
        }
        String path = SafetyConfig.getUpdatePath();
        try {
            FileUploadUtils.upgrade(path, filename, file);
        } catch (IOException e) {
            throw new ServiceException("文件上传失败");
        }
    }

    @Override
    public void update() {
        SysConfig config = new SysConfig();
        config.setConfigId(8L);
        config.setConfigValue("0");
        sysConfigService.updateConfig(config);
    }

    @Override
    public void quit() {
        SpringUtil.publishEvent(new SchemaRefreshEvent(new SchemaRefreshPayload()));
        SysConfig config = new SysConfig();
        config.setConfigId(8L);
        config.setConfigValue("1");
        sysConfigService.updateConfig(config);
        // 离线更新完知识库，同步知识库模板最新状态
        libraryTemplateConfigService.sync();
    }

    @Override
    public String version() {
        VersionInfoDTO versionInfoDTO = new VersionInfoDTO();

        try {
            ObjectMapper objectMapper = new ObjectMapper();
            File jsonFile = new File("/etc/dcas/version.json");
            versionInfoDTO = objectMapper.readValue(jsonFile, VersionInfoDTO.class);
            String edition = sysConfigService.selectConfigByKey(SysConfigEnum.SYSTEM_EDITION.getKey());
            if (StrUtil.isEmpty(edition)){
                return versionInfoDTO.getCURRENT_VERSION();
            }
            if (SysEditionEnum.EE.getEdition().equals(edition)) {
                versionInfoDTO.setCURRENT_VERSION(
                    StrUtil.concat(true, versionInfoDTO.getCURRENT_VERSION(), "（", SysEditionEnum.EE.getDesc(), "）"));
            } else if (SysEditionEnum.UE.getEdition().equals(edition)) {
                versionInfoDTO.setCURRENT_VERSION(
                    StrUtil.concat(true, versionInfoDTO.getCURRENT_VERSION(), "（", SysEditionEnum.UE.getDesc(), "）"));
            } else {
                versionInfoDTO.setCURRENT_VERSION(
                    StrUtil.concat(true, versionInfoDTO.getCURRENT_VERSION(), "（", SysEditionEnum.SE.getDesc(), "）"));
            }
            CoLicense coLicense = coLicenseService.getOne(new QueryWrapper<CoLicense>().eq("attribute", AttributeEnum.CLIENT_GRANT.getAttribute()));
            if (coLicense != null && "ONTRAIL".equals(coLicense.getLncType())){
                versionInfoDTO.setCURRENT_VERSION(
                    StrUtil.concat(true, versionInfoDTO.getCURRENT_VERSION(), "（", "测试版", "）"));
            }
        } catch (IOException e) {
            log.error("获取版本信息文件失败：{}", e.getMessage());
        }
        return versionInfoDTO.getCURRENT_VERSION();
    }

    @Override
    public void sync() {
        librarySyncService.sync();
    }

    @Override
    public LibraryHistoryVO needSync() {
        return librarySyncService.needSync();
    }

    @Override
    public Boolean lastVersion() {
        return libraryUpdateCheckService.checkVersion();
    }

    @Override
    public TerminalControlResponse queryTerminalVersion() {
        return libraryUpdateCheckService.checkSystemVersion();
    }

    @Override
    public void systemUpgrade(SystemUpgradeReq req) {
        SysConfig config = sysConfigService.selectConfigById(SysConfigEnum.MODE.getCode());
        if (Objects.isNull(config) || !Objects.equals(config.getConfigValue(), "0")) {
            throw new ServiceException("仅维护模式支持在线升级");
        }
        String path = SafetyConfig.getUpdatePath();
        String url = properties.getHost() + req.getPackageUrl();
        log.info("开始下载增量更新文件，url：{}，path：{}", url, path);
        HttpUtil.downloadFile(url, path);
    }
}
