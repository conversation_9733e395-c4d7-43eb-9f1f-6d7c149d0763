package com.dcas.system.service;

import com.dcas.common.domain.entity.TaskStep;
import com.dcas.common.model.dto.StepMsgDTO;
import com.dcas.common.model.vo.IntegrationInterfaceVO;

import java.util.List;

/**
 * 任务步骤Service接口
 * 
 * <AUTHOR>
 */
public interface ITaskStepService {
    
    /**
     * 查询任务步骤
     *
     * @param stepId 任务步骤主键
     * @return 任务步骤
     */
    TaskStep selectTaskStepByStepId(Long stepId);

    /**
     * 查询任务步骤列表
     *
     * @param taskStep 任务步骤
     * @return 任务步骤集合
     */
    List<TaskStep> selectTaskStepList(TaskStep taskStep);

    /**
     * 根据任务ID查询步骤列表
     *
     * @param taskId 任务ID
     * @return 步骤列表
     */
    List<TaskStep> selectTaskStepByTaskId(Long taskId);

    /**
     * 新增任务步骤
     *
     * @param taskStep 任务步骤
     * @return 结果
     */
    int insertTaskStep(TaskStep taskStep);

    /**
     * 修改任务步骤
     *
     * @param taskStep 任务步骤
     * @return 结果
     */
    int updateTaskStep(TaskStep taskStep);

    /**
     * 批量删除任务步骤
     *
     * @param stepIds 需要删除的任务步骤主键集合
     * @return 结果
     */
    int deleteTaskStepByStepIds(Long[] stepIds);

    /**
     * 删除任务步骤信息
     *
     * @param stepId 任务步骤主键
     * @return 结果
     */
    int deleteTaskStepByStepId(Long stepId);

    /**
     * 根据任务ID删除所有步骤
     *
     * @param taskId 任务ID
     * @return 结果
     */
    int deleteTaskStepByTaskId(Long taskId);

    /**
     * 执行单个步骤
     *
     * @param currentStep 当前步骤
     * @param nextStep 下个步骤
     * @param previousStepOutput 前一步骤的输出数据
     * @param interfaceParam 接口参数
     * @return 执行结果
     */
    StepMsgDTO executeStep(TaskStep currentStep, TaskStep nextStep, String previousStepOutput, IntegrationInterfaceVO interfaceParam, String url);

    /**
     * 获取任务的下一个待执行步骤
     *
     * @param taskId 任务ID
     * @return 下一个步骤
     */
    TaskStep getNextPendingStep(Long taskId);

    /**
     * 根据任务ID和步骤顺序获取步骤
     *
     * @param taskId 任务ID
     * @param stepOrder 步骤顺序
     * @return 任务步骤
     */
    TaskStep getTaskStepByOrder(Long taskId, Integer stepOrder);

    /**
     * 更新步骤状态
     *
     * @param stepId 步骤ID
     * @param status 新状态
     * @return 结果
     */
    boolean updateStepStatus(Long stepId, Integer status);

    /**
     * 记录步骤执行时间
     *
     * @param stepId 步骤ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param executionDuration 执行时长
     * @return 结果
     */
    boolean recordStepExecutionTime(Long stepId, java.util.Date startTime, java.util.Date endTime, Long executionDuration);

    /**
     * 记录步骤响应信息
     *
     * @param stepId 步骤ID
     * @param responseStatus 响应状态码
     * @param responseData 响应数据
     * @return 结果
     */
    boolean recordStepResponse(Long stepId, Integer responseStatus, String responseData);

    /**
     * 记录步骤错误信息
     *
     * @param stepId 步骤ID
     * @param errorMessage 错误信息
     * @return 结果
     */
    boolean recordStepError(Long stepId, String errorMessage);

    /**
     * 记录步骤输出数据
     *
     * @param stepId 步骤ID
     * @param outputData 输出数据
     * @return 结果
     */
    boolean recordStepOutput(Long stepId, String outputData);

    /**
     * 增加步骤重试次数
     *
     * @param stepId 步骤ID
     * @return 结果
     */
    boolean incrementStepRetryCount(Long stepId);

    /**
     * 统计任务中指定状态的步骤数量
     *
     * @param taskId 任务ID
     * @param status 状态
     * @return 步骤数量
     */
    int countTaskStepsByStatus(Long taskId, String status);

    /**
     * 检查步骤是否可以重试
     *
     * @param stepId 步骤ID
     * @return 是否可以重试
     */
    boolean canRetryStep(Long stepId);

    /**
     * 重置步骤状态为待执行
     *
     * @param stepId 步骤ID
     */
    void resetStepToPending(Long stepId);
}
