package com.dcas.system.config;

import java.util.concurrent.TimeUnit;

import com.dcas.common.config.SafetyConfig;

import com.dcas.system.interceptor.RepeatSubmitInterceptor;
import com.dcas.system.interceptor.impl.BaseEditionInterceptor;
import com.dcas.system.interceptor.impl.JobNumLimitInterceptor;
import com.dcas.system.interceptor.impl.LicenseInterceptor;
import com.dcas.system.interceptor.impl.ReportExportInterceptor;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.CacheControl;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;
import org.springframework.web.filter.CorsFilter;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import com.dcas.common.constant.Constants;

/**
 * 通用配置
 */
@Configuration
@RequiredArgsConstructor
public class ResourcesConfig implements WebMvcConfigurer {
    private final LicenseInterceptor licenseInterceptor;
    private final RepeatSubmitInterceptor repeatSubmitInterceptor;
    private final BaseEditionInterceptor baseEditionInterceptor;
    private final ReportExportInterceptor reportExportInterceptor;
    private final JobNumLimitInterceptor jobNumLimitInterceptor;

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        /** 本地文件上传路径 */
        registry.addResourceHandler(Constants.RESOURCE_PREFIX + "/**")
                .addResourceLocations("file:" + SafetyConfig.getProfile() + "/");

        /** swagger配置 */
        registry.addResourceHandler("/swagger-ui/**")
                .addResourceLocations("classpath:/META-INF/resources/webjars/springfox-swagger-ui/")
                .setCacheControl(CacheControl.maxAge(5, TimeUnit.HOURS).cachePublic());
        ;
    }

    /**
     * 自定义拦截规则
     */
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(repeatSubmitInterceptor).addPathPatterns("/**");
        registry.addInterceptor(licenseInterceptor).addPathPatterns("/api/project/retrieve", "/api/upgrade/start", "/api/upgrade/sync", "/api/upgrade/update");
//        registry.addInterceptor(baseEditionInterceptor).addPathPatterns( "/api/upgrade/start", "/api/upgrade/sync", "/api/upgrade/update", "/api/upgrade/version");
        registry.addInterceptor(baseEditionInterceptor).addPathPatterns( "/api/upgrade/version");
        registry.addInterceptor(reportExportInterceptor).addPathPatterns( "/api/customer/view/export/word/precheck", "/api/security/export/report/precheck",
            "/api/operation/inventory/exportExcel","/api/operation/verification/exportExcel","/api/operation/verification/exportExcel",
            "/api/operation/authority/exportWord","/api/operation/authority/exportExcel","/api/detection/export","/api/detection/exportWord",
            "/api/operation/legal/exportWord","/api/operation/legal/exportExcel","/api/operation/verification/excel/download");
        registry.addInterceptor(jobNumLimitInterceptor).addPathPatterns( "/api/security", "/api/operation/add","/api/project/add");
    }

    /**
     * 跨域配置
     */
    @Bean
    public CorsFilter corsFilter() {
        CorsConfiguration config = new CorsConfiguration();
        config.setAllowCredentials(true);
        // 设置访问源地址
        config.addAllowedOriginPattern("*");
        // 设置访问源请求头
        config.addAllowedHeader("*");
        // 设置访问源请求方法
        config.addAllowedMethod("*");
        // 有效期 1800秒
        config.setMaxAge(1800L);
        // 添加映射路径，拦截一切请求
        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**" , config);
        // 返回新的CorsFilter
        return new CorsFilter(source);
    }
}