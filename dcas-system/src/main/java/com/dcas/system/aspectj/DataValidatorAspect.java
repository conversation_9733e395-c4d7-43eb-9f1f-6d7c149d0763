package com.dcas.system.aspectj;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.parser.ParserConfig;
import com.dcas.common.annotation.DataValidator;
import com.dcas.common.enums.TemplateTypeEnum;
import com.dcas.common.model.vo.*;
import com.dcas.system.service.CommonService;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @className DataValidatorAspect
 * @description 数据校验切面
 * @date 2024/05/21 08:58
 */
@Slf4j
@Aspect
@Component
@RequiredArgsConstructor
public class DataValidatorAspect {

    private final CommonService commonService;

    @Pointcut("@annotation(com.dcas.common.annotation.DataValidator)")
    public void dataValidator() {
    }

    @Around("dataValidator()")
    public Object around(ProceedingJoinPoint pointcut) throws Throwable {
        Object obj = pointcut.proceed();
//        MethodSignature signature = (MethodSignature)pointcut.getSignature();
//        DataValidator annotation = signature.getMethod().getAnnotation(DataValidator.class);
//        TemplateTypeEnum type = annotation.type();
//        if (Objects.isNull(obj)) {
//            return null;
//        }
//        String json = JSON.toJSONString(obj);
//        // 专项评估报告，社区版全过滤
//        switch (type) {
//            case SPEC_TEMPLATE:
//                return Lists.newArrayList();
//            case ANALYSIS_TEMPLATE:
//                List<QueryModelVo> list = JSON.parseArray(json, QueryModelVo.class);
//                return list.stream().filter(QueryModelVo::getUnderstand).collect(Collectors.toList());
//            case LAW_TEMPLATE:
//                List<LegalModelVO> legalModelVOS = JSON.parseArray(json, LegalModelVO.class);
//                return legalModelVOS.stream().filter(LegalModelVO::getUnderstand).collect(Collectors.toList());
//            case RISK_TEMPLATE:
//                List<SelectedKeyValueVO> selectedKeys = JSONUtil.toList(json, SelectedKeyValueVO.class);
//                return selectedKeys.stream().filter(SelectedKeyValueVO::getUnderstand).collect(Collectors.toList());
//            case ASSET_TEMPLATE:
//                List<AssetTemplateSelectVO> assetTemplates = JSON.parseArray(json, AssetTemplateSelectVO.class);
//                return assetTemplates.stream().filter(AssetTemplateSelectVO::getUnderstand)
//                    .collect(Collectors.toList());
//            case SURVEY_TEMPLATE:
//                List<LabelVO> surveyTemplates = JSON.parseArray(json, LabelVO.class);
//                return surveyTemplates.stream().filter(LabelVO::getUnderstand).collect(Collectors.toList());
//            case SECURITY_TEMPLATE:
//                // 检查模板目前就一个基础检查模板
//                // List<SecurityTemplate> securityTemplates = JSON.parseArray(json, SecurityTemplate.class);
//                return obj;
//            default:
//                log.warn("Unknown type: {}", type);
//                break;
//        }
        return obj;
    }
}
