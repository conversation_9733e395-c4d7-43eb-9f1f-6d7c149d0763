<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcas.common.mapper.TagMapper">
    <select id="selectTagIdByName" resultType="java.lang.String">
        SELECT x.id FROM tag x
        WHERE name = #{name}
    </select>
    <select id="selectTagIdsByNameList" resultType="com.dcas.common.domain.entity.Tag">
        SELECT x.id, x.name FROM tag x
        WHERE name in
        <foreach collection="nameList" item="name" open="(" separator="," close=")">
            #{name}
        </foreach>
    </select>
    <select id="selectTagsByTagType" resultType="com.dcas.common.domain.entity.Tag">
        SELECT x.id, x.name FROM tag x
        WHERE type_id = #{tagType}
    </select>
</mapper>