<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcas.common.mapper.WorkflowTaskMapper">

    <resultMap type="WorkflowTask" id="WorkflowTaskResult">
        <id property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="capability" column="capability"/>
        <result property="productId" column="product_id"/>
        <result property="productName" column="product_name"/>
        <result property="taskType" column="task_type"/>
        <result property="taskConfig" column="task_config"/>
        <result property="currentStep" column="current_step"/>
        <result property="totalSteps" column="total_steps"/>
        <result property="progressPercentage" column="progress_percentage"/>
        <result property="errorMessage" column="error_message"/>
        <result property="executionResult" column="execution_result"/>
        <result property="verifyResult" column="verify_result"/>
        <result property="status" column="status"/>
        <result property="startTime" column="start_time"/>
        <result property="endTime" column="end_time"/>
        <result property="executionDuration" column="execution_duration"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
    </resultMap>

    <sql id="selectWorkflowTaskVo">
        select id, name, capability, product_id, product_name, logo, company, task_type, task_config,
               current_step, total_steps, progress_percentage, error_message, execution_result, verify_result,
               status, start_time, end_time, execution_duration,
               create_time, create_by, update_time, update_by
        from workflow_task
    </sql>

    <select id="selectWorkflowTaskList" parameterType="com.dcas.common.model.req.WorkflowTaskReq" resultMap="WorkflowTaskResult">
        <include refid="selectWorkflowTaskVo"/>
        <where>
            capability = #{req.capability}
            <if test="req.name != null and req.name != ''">
                AND name like concat('%', #{req.name}, '%')
            </if>
            <if test="req.productName != null and req.productName != ''">
                AND product_name like concat('%', #{req.productName}, '%')
            </if>
            <if test="req.status != null">
                AND status = #{req.status}
            </if>
            <if test="req.taskType != null">
                AND task_type = #{req.taskType}
            </if>
            <if test="req.verifyResult != null and req.verifyResult != ''">
                AND verify_result like concat('%', #{req.verifyResult}, '%')
            </if>
        </where>
        order by id desc
    </select>

    <select id="selectWorkflowTaskByTaskId" parameterType="Long" resultMap="WorkflowTaskResult">
        <include refid="selectWorkflowTaskVo"/>
        where id = #{taskId}
    </select>
    <select id="selectDetail" resultType="com.dcas.common.model.vo.WorkflowTaskDetailVO">
        select name, logo, company, verifyResult, executionResult from workflow_task where id = #{taskId}
    </select>

    <delete id="deleteWorkflowTaskByTaskId" parameterType="Long">
        delete from workflow_task where id = #{taskId}
    </delete>

    <!-- 更新任务状态 -->
    <update id="updateWorkflowTaskStatus">
        update workflow_task
        set status = #{status},
            update_time = current_timestamp,
            update_by = #{updateBy}
        where id = #{taskId}
    </update>

    <!-- 更新任务进度 -->
    <update id="updateWorkflowTaskProgress">
        update workflow_task
        set current_step = #{currentStep},
            progress_percentage = #{progressPercentage},
            update_time = current_timestamp,
            update_by = #{updateBy}
        where id = #{taskId}
    </update>

    <!-- 更新任务执行时间 -->
    <update id="updateWorkflowTaskExecutionTime">
        update workflow_task
        set start_time = #{startTime},
            end_time = #{endTime},
            execution_duration = #{executionDuration},
            update_time = current_timestamp,
            update_by = #{updateBy}
        where id = #{taskId}
    </update>

    <!-- 更新任务错误信息 -->
    <update id="updateWorkflowTaskError">
        update workflow_task
        set error_message = #{errorMessage},
            update_time = current_timestamp,
            update_by = #{updateBy}
        where id = #{taskId}
    </update>
    <update id="recordTaskComplete">
        update workflow_task
        set execution_result = #{executionResult},
            verify_result = #{verifyResult},
            update_time = current_timestamp
        where id = #{taskId}
    </update>

</mapper>
