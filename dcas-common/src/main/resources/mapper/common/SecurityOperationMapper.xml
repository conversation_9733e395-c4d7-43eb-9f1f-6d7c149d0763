<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcas.common.mapper.SecurityOperationMapper">
    <update id="updateStateById">
        update security_operation
        set status = #{status}
        where id = #{id}
    </update>
    <update id="updateCheckedStatus">
        update security_questionnaire
        set checked = #{checked}
        where id in
        <foreach collection="list" item="id" index="index" open="(" close=")" separator=",">
            (#{id})
        </foreach>
    </update>
    <update id="updateByBatchIds">
        update security_operation set del_flag='2' where id in
        <foreach collection="ids" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </update>
    <select id="pageQuery" resultType="com.dcas.common.model.vo.SecurityWorkVO">
        select t1.id,
        t1.name,
        t3.project_id,
        t3.project_name,
        t3.project_manager,
        t4.customer_name customName,
        t1.region,
        t1.industry,
        t1.template_id   as templateId,
        t1.template_name,
        t1.create_time,
        t1.create_by,
        t1.executor,
        t1.executor_account,
        t1.progress,
        t1.status,
        t1.service_content
        from security_operation t1
        left join co_project t3 on t1.project_id = t3.project_id
        left join co_customer t4 on t3.customer_id = t4.customer_id
        <where>
            t1.del_flag != '2'
            <if test="name != null and name != ''">
                and t1.name like concat('%', #{name}, '%')
            </if>
            <if test="customName != null and customName != ''">
                and t1.custom_name like concat('%', #{customName}, '%')
            </if>
            <if test="createBy != null and createBy != ''">
                and t1.create_by like concat('%', #{createBy}, '%')
            </if>
            <if test="executor != null and executor != ''">
                and t1.executor like concat('%', #{executor}, '%')
            </if>
            <if test="status != null">
                and t1.status = #{status}
            </if>
        </where>
        <!-- 数据范围过滤 -->
        ${params.dataScope}
        order by t1.create_time desc
    </select>
    <select id="selectFileIdsBySecurityId" resultType="java.lang.String">
        SELECT file_ids FROM "security_process_label" WHERE security_id = #{id} AND file_ids IS NOT NULL
    </select>
    <select id="selectServiceBySecurityId" resultType="com.dcas.common.model.dto.SecurityServiceContent">
        select model, category, content, finished
        from security_process_label
        where security_id = #{id}
    </select>
    <select id="selectDetailById" resultType="com.dcas.common.model.vo.SecurityDetailVO">
        select t1.*,
               (select count(1) from security_process_label where security_id = t1.id and length(file_ids) > 0) as fileLabel,
                t2.name as templateName
        from security_operation t1, security_template t2
        where t1.id = #{id} and t1.template_id = t2.id
    </select>
    <select id="selectOperationList" resultType="com.dcas.common.domain.entity.SecurityOperation">
        select a.id,
               a.name,
               a.region,
               a.industry,
               a.template_id   as templateId,
               a.create_time,
               a.create_by,
               a.executor,
               a.progress,
               a.status,
               a.service_content
        from security_operation a where del_flag != '2'
        <!-- 数据范围过滤 -->
        ${params.dataScope}
    </select>
    <select id="selectOperationIdsByProjectId" resultType="java.lang.Integer">
        select distinct id from security_operation where project_id = #{projectId} and del_flag != '2'
    </select>
    <select id="selectDetailByIdAndModel" resultType="com.dcas.common.model.dto.SecurityDetailDTO">
        select t1.category,
        t1.id      content_id,
        t1.sort content_sort,
        t1.content content_name,
        t2.problem,
        t2.point,
        t2.remark,
        t2.inapplicable,
        t2.inapplicable_reason,
        t2.id      questionnaire_id,
        t2.item_id,
        t3.describe itemDesc,
        t2.sort item_sort,
        t3.title   item_title,
        t2.checked
        from
            <if test="isPreview==true">
                security_label_import t1
                    inner join security_questionnaire_import t2 on t1.id = t2.label_id
            </if>
            <if test="isPreview==false">
                security_process_label t1
                    inner join security_questionnaire t2 on t1.id = t2.label_id
            </if>
                 inner join item t3 on t2.item_id = t3.id
        where t1.security_id = #{id}
          and t1.model = '数据安全检查'
        order by t1.sort, t2.sort, t2.item_id desc
    </select>
    <select id="labelIsExist" resultType="java.lang.Integer">
        SELECT count(1)
        FROM security_process_label
        WHERE security_id = #{securityId} AND service_content ~ CONCAT('(^|,)', #{labelId}, '(,|$)')
    </select>
</mapper>