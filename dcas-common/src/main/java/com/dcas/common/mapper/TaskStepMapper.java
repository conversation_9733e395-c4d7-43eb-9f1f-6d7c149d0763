package com.dcas.common.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dcas.common.domain.entity.TaskStep;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 任务步骤Mapper接口
 * 
 * <AUTHOR>
 */
public interface TaskStepMapper extends BaseMapper<TaskStep> {
    
    /**
     * 查询任务步骤
     *
     * @param stepId 任务步骤主键
     * @return 任务步骤
     */
    TaskStep selectTaskStepByStepId(Long stepId);

    /**
     * 查询任务步骤列表
     *
     * @param taskStep 任务步骤
     * @return 任务步骤集合
     */
    List<TaskStep> selectTaskStepList(TaskStep taskStep);

    /**
     * 根据任务ID查询步骤列表
     *
     * @param taskId 任务ID
     * @return 步骤列表
     */
    List<TaskStep> selectTaskStepByTaskId(Long taskId);

    /**
     * 根据任务ID和步骤顺序查询步骤
     *
     * @param taskId 任务ID
     * @param stepOrder 步骤顺序
     * @return 任务步骤
     */
    TaskStep selectTaskStepByTaskIdAndOrder(@Param("taskId") Long taskId, @Param("stepOrder") Integer stepOrder);

    /**
     * 新增任务步骤
     *
     * @param taskStep 任务步骤
     * @return 结果
     */
    int insertTaskStep(TaskStep taskStep);

    /**
     * 修改任务步骤
     *
     * @param taskStep 任务步骤
     * @return 结果
     */
    int updateTaskStep(TaskStep taskStep);

    /**
     * 删除任务步骤
     *
     * @param stepId 任务步骤主键
     * @return 结果
     */
    int deleteTaskStepByStepId(Long stepId);

    /**
     * 批量删除任务步骤
     *
     * @param stepIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteTaskStepByStepIds(Long[] stepIds);

    /**
     * 根据任务ID删除步骤
     *
     * @param taskId 任务ID
     * @return 结果
     */
    int deleteTaskStepByTaskId(Long taskId);

    /**
     * 更新步骤状态
     *
     * @param stepId 步骤ID
     * @param status 新状态
     * @param updateBy 更新者
     * @return 结果
     */
    int updateTaskStepStatus(@Param("stepId") Long stepId,
                                   @Param("status") Integer status,
                                   @Param("updateBy") String updateBy);

    /**
     * 更新步骤执行时间
     *
     * @param stepId 步骤ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param executionDuration 执行时长
     * @param updateBy 更新者
     * @return 结果
     */
    int updateTaskStepExecutionTime(@Param("stepId") Long stepId,
                                          @Param("startTime") java.util.Date startTime,
                                          @Param("endTime") java.util.Date endTime,
                                          @Param("executionDuration") Long executionDuration,
                                          @Param("updateBy") String updateBy);

    /**
     * 更新步骤响应信息
     *
     * @param stepId 步骤ID
     * @param responseStatus 响应状态码
     * @param responseData 响应数据
     * @param updateBy 更新者
     * @return 结果
     */
    int updateTaskStepResponse(@Param("stepId") Long stepId,
                                     @Param("responseStatus") Integer responseStatus,
                                     @Param("responseData") String responseData,
                                     @Param("updateBy") String updateBy);

    /**
     * 更新步骤错误信息
     *
     * @param stepId 步骤ID
     * @param errorMessage 错误信息
     * @param updateBy 更新者
     * @return 结果
     */
    int updateTaskStepError(@Param("stepId") Long stepId,
                                  @Param("errorMessage") String errorMessage,
                                  @Param("updateBy") String updateBy);

    /**
     * 更新步骤输出数据
     *
     * @param stepId 步骤ID
     * @param outputData 输出数据
     * @param updateBy 更新者
     * @return 结果
     */
    int updateTaskStepOutput(@Param("stepId") Long stepId,
                                   @Param("outputData") String outputData,
                                   @Param("updateBy") String updateBy);

    /**
     * 增加步骤重试次数
     *
     * @param stepId 步骤ID
     * @param updateBy 更新者
     * @return 结果
     */
    int incrementStepRetryCount(@Param("stepId") Long stepId, @Param("updateBy") String updateBy);

    /**
     * 根据状态查询步骤列表
     *
     * @param taskId 任务ID
     * @param status 步骤状态
     * @return 步骤列表
     */
    List<TaskStep> selectTaskStepByTaskIdAndStatus(@Param("taskId") Long taskId, @Param("status") String status);

    /**
     * 查询任务的下一个待执行步骤
     *
     * @param taskId 任务ID
     * @return 下一个步骤
     */
    TaskStep selectNextPendingStep(Long taskId);

    /**
     * 统计任务步骤数量
     *
     * @param taskId 任务ID
     * @return 步骤总数
     */
    int countTaskStepsByTaskId(Long taskId);

    /**
     * 统计任务中各状态的步骤数量
     *
     * @param taskId 任务ID
     * @param status 状态
     * @return 步骤数量
     */
    int countTaskStepsByTaskIdAndStatus(@Param("taskId") Long taskId, @Param("status") String status);
}
