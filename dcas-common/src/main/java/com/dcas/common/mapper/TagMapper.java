package com.dcas.common.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dcas.common.domain.entity.Tag;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date 2023/2/9 15:47
 * @since 1.0.1
 */
public interface TagMapper extends BaseMapper<Tag> {
    String selectTagIdByName(@Param("name") String name);

    List<Tag> selectTagIdsByNameList(@Param("nameList")List<String> nameList);

    List<Tag> selectTagsByTagType(@Param("tagType")int tagType);
}
