package com.dcas.common.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dcas.common.domain.entity.AdviseRisk;
import com.dcas.common.model.dto.AdviseRiskDTO;
import com.dcas.common.model.dto.BusSystemResultDTO;
import com.dcas.common.model.param.AdviseRiskParam;
import com.dcas.common.model.vo.AdviseRiskVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date 2024/1/25 17:54
 * @since 1.7.0
 */
public interface AdviseRiskMapper extends BaseMapper<AdviseRisk> {

    Set<String> selectItemIdToFilter(@Param("operationId") String operationId, @Param("type") Integer type);

    List<AdviseRiskVO> pageQuery(@Param("param") AdviseRiskParam param);

    List<AdviseRiskDTO> selectRiskContent(@Param("operationId") String operationId);

    List<AdviseRiskDTO> selectRiskContentContainRiskType(@Param("operationId") String operationId);

    List<AdviseRiskDTO> selectRiskContentWithLawIds(@Param("operationId")String operationId);

    List<BusSystemResultDTO> queryAdviseRiskByOperationIds(@Param("operationIds") List<String> operationIds);

    List<AdviseRiskDTO> selectRiskContentByType(@Param("operationId")String operationId, @Param("type") Integer type);

    List<AdviseRiskDTO> selectLawRiskContentByType(@Param("operationId")String operationId, @Param("type") Integer type);

    List<AdviseRiskDTO> selectLawBasicByItemIds(@Param("itemIds")List<String> itemIds, @Param("operationId")String operationId);

    List<AdviseRiskDTO> selectAnalysisBasicByItemIds(@Param("itemIds")List<String> itemIds, @Param("operationId") String operationId);
}
