package com.dcas.common.utils;

import cn.hutool.cache.impl.WeakCache;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.PropDesc;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.resource.ClassPathResource;
import cn.hutool.core.util.*;
import cn.hutool.extra.servlet.ServletUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.read.listener.PageReadListener;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.dcas.common.enums.DataSourceType;
import com.dcas.common.exception.ServiceException;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.mchz.mcdatasource.core.DataBaseType;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.ReflectionUtils;
import org.springframework.web.multipart.MaxUploadSizeExceededException;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.math.BigInteger;
import java.nio.charset.Charset;
import java.util.*;
import java.util.function.Function;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;


/**
 * <p>
 * 快捷工具类
 * </p>
 *
 * <AUTHOR>
 * @date Created in 2019-07-24 16:15
 */
@Slf4j
@UtilityClass
public class Func {
    /**
     * 驼峰
     */
    private final Pattern CAMELCASE_PATTERN = Pattern.compile("[A-Z]");


    /** 中文字符的十进制ASCII码 */
    private static final List<Integer> CHINESE_SYMBOL = com.google.common.collect.Lists.newArrayList(
        12290, 65311, 65281, 65292, 12289, 65306, 65307, 12300, 12301, 12302, 12303, 8216, 8217, 8220, 8221,
        65288, 65289, 12308, 12309, 12304, 12305, 8212, 8230, 8211, 65294, 12298, 12299, 12296, 12297, 65509);

    /**
     * 拷贝属性
     *
     * @param src   原对象
     * @param clazz 拷贝后的类
     * @param <T>   泛型
     * @return 拷贝后的对象
     */
    public <T> T toBean(Object src, Class<T> clazz) {
        return toBean(src, clazz, CopyOptions.create().ignoreCase().ignoreError().ignoreNullValue());
    }

    /**
     * 拷贝属性
     *
     * @param src     原对象
     * @param clazz   拷贝后的类
     * @param options 拷贝参数
     * @param <T>     泛型
     * @return 拷贝后的对象
     */
    public <T> T toBean(Object src, Class<T> clazz, CopyOptions options) {
        T t = ReflectUtil.newInstance(clazz);
        BeanUtil.copyProperties(src, t, options);
        return t;
    }

    public <K, V> WeakCache<K, V> newWeakCache(long timeout, long delay) {
        return new WeakCache<>(timeout);
    }

    /**
     * 包装文件路径，末尾添加文件分隔符
     *
     * @param filePath 文件路径
     * @return 带文件分隔符的文件路径
     */
    public String wrapFilePath(String filePath) {
        return StrUtil.appendIfMissing(filePath, File.separator);
    }

    public String wrapFilePath(String filePath, String... subPath) {
        String path = wrapFilePath(filePath);
        if (subPath == null) {
            return path;
        }
        return path + Stream.of(subPath).map(Func::wrapFilePath).collect(Collectors.joining());
    }

    /**
     * 仅通过文件名后缀判断文件是否是excel
     *
     * @param fileName 文件名
     * @return {@code true} excel文件
     */
    public boolean isExcelFile(String fileName) {
        if (StrUtil.isBlank(fileName)) {
            return false;
        }
        return fileName.endsWith(".xlsx") || fileName.endsWith(".xls");
    }

    /**
     * 仅通过文件名后缀判断文件是否是csv
     *
     * @param fileName 文件名
     * @return {@code true} csv文件
     */
    public boolean isCsvFile(String fileName) {
        if (StrUtil.isBlank(fileName)) {
            return false;
        }
        return fileName.endsWith(".csv");
    }


    public void writeTemplate(HttpServletResponse response, String template, String fileName) {
        write(response, new ClassPathResource("classpath://template/" + template).getStream(), fileName);
    }

    public void write(HttpServletResponse response, String filePath, String fileName) {
        write(response, FileUtil.getInputStream(filePath), fileName);
    }

    public void write(HttpServletResponse response, File file, String fileName) {
        write(response, FileUtil.getInputStream(file), fileName);
    }

    public void write(HttpServletResponse response, InputStream in, String fileName) {
        responseSetting(response, fileName);
        ServletUtil.write(response, in);
    }

    public void responseSetting(HttpServletResponse response, String fileName) {
        final String defaultValue = isExcelFile(fileName) ? "application/vnd.ms-excel;charset=utf-8" : "application/octet-stream";
        final String contentType = ObjectUtil.defaultIfNull(FileUtil.getMimeType(fileName), defaultValue);

        final Charset charset = ObjectUtil.defaultIfNull(Charset.forName(response.getCharacterEncoding()), CharsetUtil.CHARSET_UTF_8);
        fileName = new String(fileName.getBytes(), charset);
        response.setHeader("Content-Disposition", StrUtil.format("attachment;filename={}", URLUtil.encode(fileName, charset)));
        response.setContentType(contentType);
        response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
    }

    public <T> String name(SlFunction<T, ?> fun) {
        return SerializedLambda.getColumnName(fun);
    }

    public <T> List<String> getAllFields(Class<T> clazz) {
        return BeanUtil.getBeanDesc(clazz).getProps().stream().map(PropDesc::getRawFieldName).collect(Collectors.toList());
    }

    public Set<DataSourceType> useKerberosSource() {
        return DatabaseOptionUtil.USE_KERBEROS_SOURCE;
    }

    /**
     * 是否存在
     *
     * @param count 数量
     * @return boolean
     */
    public boolean exist(Integer count) {
        if (count == null) {
            return false;
        }
        return count > 0;
    }

    /**
     * 是否存在
     *
     * @param count 数量
     * @return boolean
     */
    public boolean exist(String count) {
        return Objects.nonNull(count);
    }

    /**
     * 是否存在
     *
     * @param count 数量
     * @return boolean
     */
    public boolean exist(Long count) {
        if (count == null) {
            return false;
        }
        return count > 0;
    }

    /**
     * 是否存在
     *
     * @param collection 列表
     * @return boolean
     */
    public boolean exist(Collection<?> collection) {
        return CollUtil.isNotEmpty(collection);
    }

    /**
     * 判断列表数据是否为空
     *
     * @param collection 列表
     * @return boolean
     */
    public boolean isEmpty(Collection<?> collection) {
        return CollUtil.isEmpty(collection);
    }

    /**
     * 判断列表数据是否不为空
     *
     * @param collection 列表
     * @return boolean
     */
    public boolean isNotEmpty(Collection<?> collection) {
        return CollUtil.isNotEmpty(collection);
    }

    /**
     * 状态
     *
     * @param status 状态
     * @return status > 0 ? true : false
     */
    public boolean status(Integer status) {
        if (Objects.isNull(status)) {
            return false;
        }
        return status > 0;
    }

    /**
     * 是否为null
     *
     * @param obj 对象
     * @return 是否为null
     */
    public boolean isNull(Object obj) {
        return ObjectUtil.isNull(obj);
    }

    /**
     * 是否为null
     *
     * @param obj 对象
     * @return 是否为null
     */
    public boolean isNotNull(Object obj) {
        return ObjectUtil.isNotNull(obj);
    }

    public boolean isNullStr(String str) {
        return StrUtil.isBlank(str) || StrUtil.NULL.equals(str);
    }

    /**
     * 转换常见值为boolean值
     *
     * @param obj 例如0 1
     * @return boolean
     */
    public boolean isTrue(Object obj) {
        return BooleanUtil.toBoolean(String.valueOf(obj));
    }

    public <T> String buildSysConfigKey(String pre, SlFunction<T, ?> fun) {
        return buildSysConfigKey(pre, SerializedLambda.getColumnName(fun));
    }

    public String buildSysConfigKey(String pre, String name) {
        return pre + StrUtil.DOT + name;
    }

    public DateTime now(){
        return DateUtil.date();
    }

    //DatabaseOptionUtil***********************************************************************************************

    public String getStrTrim(Map<String, ?> map, String key, String defaultValue) {
        Object o = map.get(key);
        if (null == o) {
            return defaultValue;
        }
        return StrUtil.trim(o.toString());
    }

    public String wrapperIpRange(String startIp, String endIp) {
        return String.format("%s-%s", startIp, endIp);
    }

    public static <R, T> List<R> streamMap(Collection<T> collection, Function<? super T, ? extends R> mapper) {
        if (Objects.isNull(collection) || CollectionUtils.isEmpty(collection)) {
            return Lists.newArrayList();
        }
        return collection.stream()//
            .map(mapper)//
            .filter(Objects::nonNull)//
            .distinct()//
            .collect(Collectors.toList());
    }

    public static <R, T> List<R> streamMapLink(Collection<T> collection, Function<? super T, ? extends R> mapper) {
        return new LinkedList<>(streamMap(collection, mapper));
    }

    public static List<String> toList(JSONArray jsonArray) {
        return jsonArray.stream().map(Func::toString).collect(Collectors.toList());
    }

    public static List<Map<String, String>> toMap(JSONArray jsonArray) {
        List<Map<String, String>> maps = new ArrayList<>();
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject jsonObject = jsonArray.getJSONObject(i);
            HashMap<String, String> map = Maps.newHashMap();
            jsonObject.forEach((key, value) -> map.put(key, Func.toString(value)));
            maps.add(map);
        }
        return maps;
    }

    public static String toString(Object o) {
        if (Objects.isNull(o)) {
            return null;
        }
        if (o instanceof String) {
            return (String) o;
        }
        return o.toString();
    }

    /**
     * list1和list2去交集
     * @param list1 必须已排序
     * @param list2 必须已排序
     */
    public static <T extends Comparable<T>> void retainAll(List<T> list1, List<T> list2) {
        if (CollectionUtils.isEmpty(list1) || CollectionUtils.isEmpty(list2)) {
            return;
        }
        Iterator<T> iterator1 = list1.iterator();
        Iterator<T> iterator2 = list2.iterator();
        T t1 = null;
        T t2 = null;
        while (true) {
            t1 = Objects.isNull(t1) ? iterator1.next() : t1;
            t2 = Objects.isNull(t2) ? iterator2.next() : t2;
            int i = t1.compareTo(t2);
            if (i == 0) {
                iterator1.remove();
                iterator2.remove();
                t1 = null;
                t2 = null;
            } else if (i > 0) {
                t2 = null;
            } else {
                // i < 0
                t1 = null;
            }
            if (!iterator1.hasNext() || !iterator2.hasNext()) {
                return;
            }
        }
    }

    public static <T> T ifNull(T val1, T val2) {
        return Objects.nonNull(val1) ? val1 : val2;
    }

    public static <T> boolean isUnique(List<T> list) {
        return CollectionUtils.isNotEmpty(list) && list.size() == 1;
    }

    public static <T> boolean isNotUnique(List<T> list) {
        return CollectionUtils.isNotEmpty(list) && list.size() > 1;
    }

    public static String getMessage(Throwable e) {
        Objects.requireNonNull(e);
        StringWriter sw = new StringWriter();
        PrintWriter pw = new PrintWriter(sw);
        e.printStackTrace(pw);
        return sw.toString();
    }

    public static String getMessageLimit(Throwable e, int limit) {
        String message = getMessage(e);
        return StrUtil.sub(message, 0, limit);
    }

    /**
     * 校验是否存在为空的属性（任一属性为空既满足）
     *
     * @param object 检查对象
     * @param clazz 对象类型
     * @param <T> 泛型
     * @return {@link boolean}
     */
    public static <T> boolean isMissing(Object object, Class<T> clazz) {
        if (Func.isNull(object)) {
            return true;
        }
        Field[] fields = ReflectUtil.getFields(clazz);
        for (Field field : fields) {
            Object fieldValue = ReflectUtil.getFieldValue(object, field);
            if (ObjectUtil.isNull(fieldValue)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 获取集合中最接近的数
     * @param number    需要查找的数字
     * @param numbers   数字集合
     * @param flag   如果有两个相近的数据   true:选择大数  false:选择小数
     * @param <T>   必须为数字类型
     * @return  相近结果
     */
    public <T extends Number> T getSimilarNumber (T number, Collection<T> numbers, Boolean flag){
        if (null == numbers || numbers.isEmpty()) {
            throw new RuntimeException("数字集合不能为空");
        }
        if (numbers.contains(number)) {
            return number;
        }
        if (numbers instanceof List) {
            numbers = new HashSet<>(numbers);
        }
        numbers.add(number);
        List<T> numList = numbers.stream().sorted().collect(Collectors.toList());
        int size = numList.size();
        int index = numList.indexOf(number);
        if (index <= 0) {
            return numList.get(1);
        }
        if (index >= size - 1) {
            return numList.get(size - 2);
        }
        T before = numList.get(index - 1);
        T after = numList.get(index + 1);
        double beforeDifference = number.doubleValue() - before.doubleValue();
        double afterDifference = after.doubleValue() - number.doubleValue();
        if (String.valueOf(beforeDifference).equals(String.valueOf(afterDifference))) {
            return flag ? after : before;
        }
        return beforeDifference < afterDifference ? before : after;
    }

    /**
     * 返回符号前的子串
     *
     * @param str 待检查字符串
     * @return 处理后的字符串
     */
    public String cutBeforeSymbol(String str) {
        for (int i = 0; i < str.length(); i++) {
            if (isSymbol(str.charAt(i))) {
                return str.substring(0, i);
            }
        }
        return str;
    }

    /**
     * 判断字符是否是符号（中文符号不连续）
     *
     * @param c 指定字符
     * @return boolean
     */
    private boolean isSymbol(char c) {
        return c <= 47 || (c >= 58 && c <= 64) || (c >= 91 && c <= 96) || (c >= 123 && c <= 255)
            || CHINESE_SYMBOL.contains((int)c);
    }

    public Integer getIntegerId(Long id) {
        if (Objects.isNull(id)) {
            return null;
        }
        return Integer.valueOf(id.intValue());
    }

    public char[] initChars() {
        // 0-9 [48-57]; A-Z [65-90]; a-z [97-122]
        return new char[]{ 48, 49, 50, 51, 52, 53, 54, 55, 56, 57,
            65, 67, 68, 69, 70, 71, 72, 73, 74, 75,
            76, 77, 78, 79, 80, 81, 82, 83, 84, 85,
            86, 87, 88, 89, 90, 97, 98, 99, 100, 101,
            102, 103, 104, 105, 106, 107, 108, 109,
            110, 111, 112, 113, 114, 115, 116, 117,
            118, 119, 120, 121, 122 };
    }


    public String judgeCharacter(String str) {
        // 截取首字符之前的字符串
        for (int i = 0; i < str.length(); i++) {
            char ch = str.charAt(i);
            if (notExists(ch)) {
                return str.substring(0, i);
            }
        }
        return str;
    }

    private  boolean notExists(char ch) {
        if (ch > 255) {
            return false;
        }
        char[] chars = initChars();
        for (int i = 0; i < chars.length; i++) {
            if (ch != chars[i]) {
                return true;
            }
        }
        return false;
    }

    /**
     * 导入文件校验并映射为实体
     *
     * @param file 导入文件
     * @param clazz 实体映射类.class
     * @param sheetName sheet页名称
     * @param ignoreEmptyRow 是否忽略空行
     * @return List<T>
     * @param <T> 泛型 excel实体映射
     * @throws IOException io异常
     */
    public static <T> List<T> fileAttestation(MultipartFile file, Class<T> clazz, String sheetName, boolean ignoreEmptyRow) throws IOException {
        String fileName = file.getOriginalFilename();
        List<T> importList;
        if (file.getSize() > 104857600L) {
            throw new MaxUploadSizeExceededException(104857600L);
        }
        //仅支持 .xls .xlsx格式文件
        if(Func.isExcelFile(fileName)){
            ExcelReader reader = StrUtil.isEmpty(sheetName) ?
                ExcelUtil.getReader(file.getInputStream()) :
                ExcelUtil.getReader(file.getInputStream(), sheetName);
            reader.setIgnoreEmptyRow(ignoreEmptyRow);
            importList = reader.readAll(clazz);
        } else {
            throw new ServiceException("不支持的导入文件格式");
        }
        if(CollectionUtil.isEmpty(importList)){
            throw new ServiceException("导入文件为空");
        }
        // 去除掉excel文档中尾部内容为空，但由于存在格式等原因导致读入的空对象
        // （仅排除尾部，如果需要排除所有空行直接设置ignoreEmptyRow=true）
        for (int i = importList.size() - 1; i >= 0; i--) {
            if (!isEmpty(importList.get(i), clazz)) {
                if (i != importList.size() - 1) {
                    return CollUtil.sub(importList, 0, i + 1);
                }
                break;
            }
        }
        return importList;
    }

    public static <T> List<T> fileAttestationPart(MultipartFile file, Class<T> clazz) throws IOException {
        return fileAttestationPart(file, clazz, 1);
    }

    public static <T> List<T> fileAttestationPart(MultipartFile file, Class<T> clazz, long sheetNo) throws IOException {
        return fileAttestationPart(file, clazz, 1, sheetNo);
    }

    /**
     * 分批次读取excel，不会导致OOM
     *
     * @param file excel文件
     * @param clazz 类信息
     * @param headRowNum 标题行数 例如2表示标题有2行，数据从第三行开始读。默认是1
     * @return 返回读取内容
     * @param <T> 泛型
     */
    public static <T> List<T> fileAttestationPart(MultipartFile file, Class<T> clazz, int headRowNum) throws IOException {
        return fileAttestationPart(file, clazz, headRowNum, 0);
    }

    public static <T> List<T> fileAttestationPart(MultipartFile file, Class<T> clazz, int headRowNum, long sheetNo) throws IOException {
        List<T> result = new ArrayList<>();
        EasyExcel.read(file.getInputStream(), clazz, new PageReadListener<T>(result::addAll)).sheet((int) sheetNo).headRowNumber(headRowNum).doRead();
        return result;
    }

    /**
     * 判断对象是否为空对象，属性都为<code>null</code>
     *
     * @param object 对象
     * @param clazz  对象类型
     * @return 是否为空，<code>true</code> - 空 / <code>false</code> - 非空
     */
    public static <T> Boolean isEmpty(Object object, Class<T> clazz) {
        if (Func.isNull(object)) {
            return true;
        }
        Field[] fields = ReflectUtil.getFields(clazz);
        for (Field field : fields) {
            Object fieldValue = ReflectUtil.getFieldValue(object, field);
            if (ObjectUtil.isNotNull(fieldValue)) {
                return false;
            }
        }
        return true;
    }

    /**
     * 合并
     */
    public static <R, T> Map<R, Set<T>> mergeMaps(Map<R, Set<T>> map1, Map<R, Set<T>> map2) {
        Map<R, Set<T>> mergedMap = new HashMap<>();

        Set<R> keys = new HashSet<>();
        keys.addAll(map1.keySet());
        keys.addAll(map2.keySet());

        for (R key : keys) {
            Set<T> values1 = map1.get(key);
            Set<T> values2 = map2.get(key);

            Set<T> mergedValues = new HashSet<>();

            if (values1 != null) {
                mergedValues.addAll(values1);
            }

            if (values2 != null) {
                mergedValues.addAll(values2);
            }

            mergedMap.put(key, mergedValues);
        }

        return mergedMap;
    }

    public static <T> void export(HttpServletResponse response, Collection<T> data, String fileName) throws IOException {
        try (ExcelWriter writer = ExcelUtil.getWriter()) {
            writer.setOnlyAlias(true);
            writer.write(data, true);
            responseSetting(response,
                    String.format("%s-%s.xlsx", fileName, DateUtils.getExportDateStr(System.currentTimeMillis())));

            ServletOutputStream out = response.getOutputStream();
            writer.flush(out, true);
        }
    }

    /**
     * 插入前填充数据结果
     * @param obj 对象
     */
    public static void beforeInsert(Object obj) {
        if (null == obj) {
            return;
        }
        Class<?> clazz = obj.getClass();
        Method setCreateTime = ReflectionUtils.findMethod(clazz, "setCreateTime", Date.class);
        Method setCreateBy = ReflectionUtils.findMethod(clazz, "setCreateBy", String.class);

        assert setCreateTime != null;
        ReflectionUtils.invokeMethod(setCreateTime, obj, DateUtil.date());
        assert setCreateBy != null;
        ReflectionUtils.invokeMethod(setCreateBy, obj, SecurityUtils.getUsername());
    }

    /**
     * 更新前填充数据结果
     * @param obj 对象
     */
    public static void beforeUpdate(Object obj) {
        if (null == obj) {
            return;
        }
        Class<?> clazz = obj.getClass();
        Method setUpdateTime = ReflectionUtils.findMethod(clazz, "setUpdateTime", Date.class);
        Method setUpdateBy = ReflectionUtils.findMethod(clazz, "setUpdateBy", String.class);

        assert setUpdateTime != null;
        ReflectionUtils.invokeMethod(setUpdateTime, obj, new Date());
        assert setUpdateBy != null;
        ReflectionUtils.invokeMethod(setUpdateBy, obj, SecurityUtils.getUsername());
    }

    /**
     * 通过传入的键值对构建json字符串
     *
     * @param keyValuePairs 键值对
     * @return json字符串
     */
    public static String buildJsonString(Map<String, Object> keyValuePairs) {
        try {
            Gson gson = new Gson();
            JsonObject jsonObject = new JsonObject();
            keyValuePairs.forEach((key, value) -> jsonObject.add(key, gson.toJsonTree(value)));
            return gson.toJson(jsonObject);
        } catch (Exception e) {
            throw new ServiceException(String.format("构建json字符串失败：%s", e.getMessage()));
        }
    }

    public static String[] splitCatalogAndSchema(DataBaseType dataBaseType, String catalogSchema) {
        String[] s = new String[2];
        String leftPatch = "";
        String rightPatch = "";
        if (StringUtils.isEmpty(catalogSchema)) {
            return s;
        }
        String splitStr = null;
        if ((Objects.equals(DataBaseType.MSSQL, dataBaseType) || Objects.equals(DataBaseType.RDS_MSSQL, dataBaseType))//
                && catalogSchema.contains(StrUtil.DOT)) {
            if(catalogSchema.startsWith("[")){
                splitStr = "].";
                leftPatch = "]";
            }else{
                splitStr = StrUtil.DOT;
            }
        } else if (catalogSchema.contains(StrUtil.COLON)) {
            splitStr = StrUtil.COLON;
        }
        if (Objects.isNull(splitStr)) {
            s[1] = catalogSchema;
        } else {
            String[] split = StrUtil.splitToArray(catalogSchema, splitStr);
            s[0] = split[0] + leftPatch;
            s[1] = split[1] + rightPatch;
        }
        return s;
    }

    public void isInterrupted() throws InterruptedException {
        if (Thread.currentThread().isInterrupted()) {
            throw new InterruptedException();
        }
    }

    public static void isInterrupted(Thread supperThread) {
        if (Objects.nonNull(supperThread) && supperThread.isInterrupted()) {
            throw new RuntimeException(new InterruptedException());
        }
    }

    public static Throwable getCause(Throwable throwable) {
        Throwable throwableCause = throwable.getCause();
        if(throwableCause !=null){
            return getCause(throwableCause);
        }else {
            return throwable;
        }
    }

    /**
     * 将输入流的byte转成十六进制
     */
    public String bytes2HexString(byte[] bytes) {
        if (bytes.length == 0) {
            return null;
        }
        int num = Math.min(10, bytes.length);
        byte[] tmp = new byte[10];
        System.arraycopy(bytes, 0, tmp, 0, num);
        return new BigInteger(1, tmp).toString(16);
    }
}
