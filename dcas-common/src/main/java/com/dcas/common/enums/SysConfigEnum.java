package com.dcas.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <p>
 *     sys_config 系统配置枚举
 * </p>
 *
 * <AUTHOR>
 * @date 2023/1/11 10:20
 * @since 1.0.1
 */
@Getter
@AllArgsConstructor
public enum SysConfigEnum {

    SKIN(1L, "主题皮肤", "sys.index.skinName"),
    PASSWORD(2L, "初始密码", "sys.user.initPassword"),
    THEME(3L, "系统主题", "sys.index.sideTheme"),
    CAPTCHA(4L, "验证码", "sys.account.captchaEnabled"),
    REGISTER(5L, "用户注册", "sys.account.registerUser"),
    SOURCE_TYPE(6L, "数据源类型", "sys.source.basic"),
    SOURCE_CONFIG(7L, "数据源配置", "sys.source.details"),
    MODE(8L, "系统模式", "sys.mode.status"),
    MACHINE_CODE(9L, "本机识别码", "sys.machine.id"),
    TERMINAL_CODE(10L, "终端识别码", "sys.terminal.id"),
    SYSTEM_VERSION(11L, "系统版本", "sys.app.version"),
    SCAN_TOKEN(12L, "漏扫TOKEN", "sys.scan.token"),
    SCAN_USER(13L, "漏扫USER", "sys.scan.user"),
    SYSTEM_EDITION(14L, "系统版本类型", "sys.app.edition"),
    SCAN_HOST(15L, "漏扫主机IP地址", "sys.scan.host"),
    JOB_NUM(16L, "系统作业数量", "sys.job.num"),
    ;


    private final Long code;
    private final String name;
    private final String key;
}
