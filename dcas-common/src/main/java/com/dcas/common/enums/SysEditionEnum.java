package com.dcas.common.enums;

import lombok.Getter;

/**
 * 系统版本类型
 *
 * <AUTHOR>
 * @date 2024/03/14 09:51
 **/
@Getter
public enum SysEditionEnum {
    /**
     *
     */
    SE("SE", "标准版"),
    EE("EE", "企业版"),
    UE("UE", "旗舰版");

    private final String edition;
    private final String desc;

    SysEditionEnum(String edition, String desc) {
        this.edition = edition;
        this.desc = desc;
    }
}
