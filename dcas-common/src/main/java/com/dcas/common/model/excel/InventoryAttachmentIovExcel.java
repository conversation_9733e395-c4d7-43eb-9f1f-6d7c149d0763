package com.dcas.common.model.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date 2024/6/12 16:52
 * @since 1.6.6
 */
@Data
@Builder
@AllArgsConstructor
public class InventoryAttachmentIovExcel {


    @ExcelProperty(value = "系统名称", index = 0)
    private String busSystem;

    @ExcelProperty(value = "模式名", index = 1)
    private String schemaName;

    @ExcelProperty(value = "数据资产", index = 2)
    private String dataAsset;

    @ExcelProperty(value = "资产注释", index = 3)
    private String assetComment;

    @ExcelProperty(value = "敏感等级", index = 4)
    private Integer sensitiveLevel;

    @ExcelProperty(value = "资产综合风险等级", index = 5)
    private String riskLevel;
}
