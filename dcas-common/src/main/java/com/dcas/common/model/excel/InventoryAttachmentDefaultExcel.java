package com.dcas.common.model.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date 2024/6/12 16:52
 * @since 1.6.6
 */
@Data
@Builder
@AllArgsConstructor
public class InventoryAttachmentDefaultExcel {


    @ExcelProperty(value = "所属业务系统", index = 0)
    private String busSystem;

    @ExcelProperty(value = "资产名称", index = 1)
    private String dataAsset;

    @ExcelProperty(value = "敏感等级", index = 2)
    private String sensitiveLevel;

    @ExcelProperty(value = "数据采集风险系数", index = 3)
    private String dataCollegeRiskFactor;

    @ExcelProperty(value = "数据采集风险值", index = 4)
    private String dataCollegeRiskValue;

    @ExcelProperty(value = "数据采集风险等级", index = 5)
    private String dataCollegeRiskLevel;

    @ExcelProperty(value = "数据传输风险系数", index = 6)
    private String dataTransRiskFactor;

    @ExcelProperty(value = "数据传输风险值", index = 7)
    private String dataTransRiskValue;

    @ExcelProperty(value = "数据传输风险等级", index = 8)
    private String dataTransRiskLevel;

    @ExcelProperty(value = "数据存储风险系数", index = 9)
    private String dataStorageRiskFactor;

    @ExcelProperty(value = "数据存储风险值", index = 10)
    private String dataStorageRiskValue;

    @ExcelProperty(value = "数据存储风险等级", index = 11)
    private String dataStorageRiskLevel;

    @ExcelProperty(value = "数据处理风险系数", index = 12)
    private String dataProcessRiskFactor;

    @ExcelProperty(value = "数据处理风险值", index = 13)
    private String dataProcessRiskValue;

    @ExcelProperty(value = "数据处理风险等级", index = 14)
    private String dataProcessRiskLevel;


    @ExcelProperty(value = "数据交换风险系数", index = 15)
    private String dataChangeRiskFactor;

    @ExcelProperty(value = "数据交换风险值", index = 16)
    private String dataChangeRiskValue;

    @ExcelProperty(value = "数据交换风险等级", index = 17)
    private String dataChangeRiskLevel;


    @ExcelProperty(value = "数据销毁风险系数", index = 18)
    private String dataDisposeRiskFactor;

    @ExcelProperty(value = "数据销毁风险值", index = 19)
    private String dataDisposeRiskValue;

    @ExcelProperty(value = "数据销毁风险等级", index = 20)
    private String dataDisposeRiskLevel;


    @ExcelProperty(value = "通用风险系数", index = 21)
    private String generalRiskFactor;

    @ExcelProperty(value = "通用风险值", index = 22)
    private String generalRiskValue;

    @ExcelProperty(value = "通用风险等级", index = 23)
    private String generalRiskLevel;

    @ExcelProperty(value = "资产综合风险等级", index = 24)
    private String riskLevel;
}
