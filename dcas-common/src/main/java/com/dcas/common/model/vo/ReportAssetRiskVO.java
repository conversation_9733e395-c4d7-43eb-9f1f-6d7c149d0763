package com.dcas.common.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date 2023/3/8 15:16
 * @since 1.2.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ReportAssetRiskVO {

    private Integer sort;

    private String assetName;

    private String assetComment;

    private String risk;

    private String riskFactor;
}
