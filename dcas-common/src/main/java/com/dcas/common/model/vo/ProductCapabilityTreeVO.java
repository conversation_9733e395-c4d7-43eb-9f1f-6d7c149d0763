package com.dcas.common.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 产品能力树形结构VO
 *
 * <AUTHOR>
 */
@Data
@ApiModel("产品能力树形结构")
public class ProductCapabilityTreeVO {

    @ApiModelProperty("能力名称")
    private String capabilityName;

    /**
     * 能力Id
     */
    @ApiModelProperty("能力ID")
    private String capability;
    
    /**
     * 该能力下的产品列表
     */
    @ApiModelProperty("该能力下的产品列表")
    private List<ProductInfoVO> products;
    
    /**
     * 产品信息VO
     */
    @Data
    @ApiModel("产品信息")
    public static class ProductInfoVO {
        /**
         * 能力ID
         */
        @ApiModelProperty("能力ID")
        private Long releaseId;
        
        /**
         * 产品名称
         */
        @ApiModelProperty("产品名称")
        private String productName;
    }
}
