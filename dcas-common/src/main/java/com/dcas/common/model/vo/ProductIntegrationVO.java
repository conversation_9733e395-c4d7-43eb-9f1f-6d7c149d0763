package com.dcas.common.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 产品集成展示VO
 *
 * <AUTHOR>
 */
@Data
@ApiModel("产品集成展示")
public class ProductIntegrationVO {

    /**
     * 产品能力
     */
    @ApiModelProperty("产品能力ID")
    private String capability;

    @ApiModelProperty("产品能力名称")
    private String capabilityName;

    @ApiModelProperty("产品列表")
    private List<ProductInfoVO> products;

}
