package com.dcas.common.model.req;

import com.dcas.common.model.vo.IntegrationFormFieldVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 创建集成请求
 *
 * <AUTHOR>
 */
@Data
@ApiModel("创建集成请求")
public class CreateIntegrationReq {
    
    /**
     * 关联API版本记录ID
     */
    @ApiModelProperty(value = "关联API版本记录ID", required = true)
    @NotNull(message = "API版本记录ID不能为空")
    private Long releaseId;

    @ApiModelProperty(value = "产品能力", required = true)
    @NotBlank(message = "产品能力不能为空")
    private String capability;

    @ApiModelProperty(value = "产品名称", required = true)
    @NotBlank(message = "产品名称不能为空")
    private String name;

    @ApiModelProperty(value = "产品地址", required = true)
    @NotBlank(message = "产品地址不能为空")
    private String url;

    @ApiModelProperty(value = "产品类型名称", required = true)
    @NotBlank(message = "产品类型名称不能为空")
    private String productName;

    /**
     * 配置参数
     */
    @Size(min = 1, message = "配置参数不能为空")
    @ApiModelProperty("配置参数")
    private List<IntegrationFormFieldVO> configParams;
}
