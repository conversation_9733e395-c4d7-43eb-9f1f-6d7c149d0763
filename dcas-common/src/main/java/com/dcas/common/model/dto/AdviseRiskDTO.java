package com.dcas.common.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date 2024/1/26 10:31
 * @since 1.7.0
 */
@Data
public class AdviseRiskDTO {

    @ApiModelProperty(value = "主键")
    @NotNull(message = "主键不能为空")
    private Integer id;

    @ApiModelProperty(value = "核查项ID")
    private String itemId;

    @ApiModelProperty(value = "业务系统ID")
    private String systemId;

    @ApiModelProperty(value = "风险分类")
    @NotBlank(message = "风险分类不能为空")
    private String riskType;

    @ApiModelProperty(value = "风险分析")
    @NotBlank(message = "风险分析不能为空")
    private String content;

    @ApiModelProperty(value = "风险等级")
    @NotNull(message = "风险等级不能为空")
    private Integer riskLevel;

    @ApiModelProperty(value = "依据")
    private String basis;

    @ApiModelProperty(value = "风险描述")
    private String describe;

    @ApiModelProperty(value = "标签id")
    private String tagIds;

    @NotBlank(message = "风险分类")
    private Long riskTypeTag;

}
