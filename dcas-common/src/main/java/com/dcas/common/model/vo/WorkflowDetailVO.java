package com.dcas.common.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date 2025/6/19 15:14
 * @since 1.0.0
 */
@Data
public class WorkflowDetailVO {

    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("任务名称")
    private String name;

    @ApiModelProperty("任务类型 1-能力验证 2-结果验证")
    private Integer taskType;

    @ApiModelProperty(value = "产品ID")
    private Long productId;

    @ApiModelProperty(value = "产品名称")
    private String productName;

    @ApiModelProperty("接口配置参数")
    private List<IntegrationInterfaceVO> taskConfig;
}
