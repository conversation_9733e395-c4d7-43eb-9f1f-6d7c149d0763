package com.dcas.common.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date 2023/7/3 10:41
 * @since 1.4.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("security_operation")
public class SecurityOperation {

    @TableId(type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "作业名称")
    private String name;

    /**
     * 将来可能会删除客户名称字段，1.5.0之后新建作业需要与项目绑定，取项目中的客户名称
     */
    @Deprecated
    @ApiModelProperty(value = "客户名称")
    private String customName;

    @ApiModelProperty(value = "项目id")
    private String projectId;

    @ApiModelProperty(value = "涉及地域")
    private String region;

    @ApiModelProperty(value = "涉及行业")
    private String industry;

    @ApiModelProperty(value = "检查过程内容")
    private String serviceContent;

    @ApiModelProperty(value = "模板id")
    private Integer templateId;

    @ApiModelProperty(value = "模板名称")
    private String templateName;

    @ApiModelProperty(value = "模板分数")
    private Integer templateScore;

    @ApiModelProperty(value = "作业进度")
    private Integer progress;

    @ApiModelProperty(value = "执行状态 1-待启动 2-执行中 3-已结项")
    private Byte status;

    @ApiModelProperty(value = "执行人")
    private String executor;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "创建人")
    private String createBy;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    @ApiModelProperty(value = "更新人")
    private String updateBy;

    @ApiModelProperty(value = "知识库版本")
    private String version;

    @ApiModelProperty(value = "用户ID")
    private Long userId;

    @ApiModelProperty(value = "部门ID")
    private Long deptId;

    @ApiModelProperty(value = "执行人账号")
    private String executorAccount;

    @ApiModelProperty(value = "删除标志")
    private String delFlag;
}
