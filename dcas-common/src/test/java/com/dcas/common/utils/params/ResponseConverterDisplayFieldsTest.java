package com.dcas.common.utils.params;

import cn.hutool.json.JSONUtil;
import com.dcas.common.model.other.OutParam;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * ResponseConverter显示字段过滤功能测试类
 */
public class ResponseConverterDisplayFieldsTest {

    @Test
    public void testFilterDisplayFields_SimpleObject() {
        // 测试简单对象的显示字段过滤
        String response = "{\"data\":{\"id\":1,\"name\":\"John\",\"age\":25,\"email\":\"<EMAIL>\",\"internal_id\":\"xyz\"}}";

        OutParam outParam = new OutParam();
        List<OutParam.ColumnParam> columnList = new ArrayList<>();

        // 配置显示字段：name, email
        OutParam.ColumnParam nameColumn = new OutParam.ColumnParam();
        nameColumn.setColumnName("name");
        nameColumn.setDisplayName("姓名");
        nameColumn.setLocation("data");
        columnList.add(nameColumn);

        OutParam.ColumnParam emailColumn = new OutParam.ColumnParam();
        emailColumn.setColumnName("email");
        emailColumn.setDisplayName("邮箱");
        emailColumn.setLocation("data");
        columnList.add(emailColumn);

        outParam.setColumnList(columnList);

        String result = ResponseConverter.filterDisplayFields(response, outParam);

        assertNotNull(result);
        // 验证结构化表格格式
        assertTrue(result.contains("\"head\""));
        assertTrue(result.contains("\"data\""));

        // 验证表头信息
        assertTrue(result.contains("\"dataIndex\":\"name\""));
        assertTrue(result.contains("\"title\":\"姓名\""));
        assertTrue(result.contains("\"key\":\"name\""));
        assertTrue(result.contains("\"dataIndex\":\"email\""));
        assertTrue(result.contains("\"title\":\"邮箱\""));
        assertTrue(result.contains("\"key\":\"email\""));

        // 验证数据行（使用columnName作为key）
        assertTrue(result.contains("\"name\":\"John\""));
        assertTrue(result.contains("\"email\":\"<EMAIL>\""));
        assertFalse(result.contains("\"age\""));
        assertFalse(result.contains("\"internal_id\""));

        System.out.println("Original: " + response);
        System.out.println("Filtered: " + result);
    }

    @Test
    public void testFilterDisplayFields_ArrayData() {
        // 测试数组数据的显示字段过滤
        String response = "{\"users\":[{\"id\":1,\"name\":\"John\",\"age\":25,\"email\":\"<EMAIL>\"},{\"id\":2,\"name\":\"Jane\",\"age\":30,\"email\":\"<EMAIL>\"}]}";

        OutParam outParam = new OutParam();
        List<OutParam.ColumnParam> columnList = new ArrayList<>();

        // 配置显示字段：只显示name
        OutParam.ColumnParam nameColumn = new OutParam.ColumnParam();
        nameColumn.setColumnName("name");
        nameColumn.setDisplayName("姓名");
        nameColumn.setLocation("users");
        columnList.add(nameColumn);

        outParam.setColumnList(columnList);

        String result = ResponseConverter.filterDisplayFields(response, outParam);

        assertNotNull(result);
        // 验证结构化表格格式
        assertTrue(result.contains("\"head\""));
        assertTrue(result.contains("\"data\""));

        // 验证表头信息
        assertTrue(result.contains("\"dataIndex\":\"name\""));
        assertTrue(result.contains("\"title\":\"姓名\""));
        assertTrue(result.contains("\"key\":\"name\""));

        // 验证数据行（使用columnName作为key）
        assertTrue(result.contains("\"name\":\"John\""));
        assertTrue(result.contains("\"name\":\"Jane\""));
        assertFalse(result.contains("\"age\""));
        assertFalse(result.contains("\"email\""));
        assertFalse(result.contains("\"id\""));

        System.out.println("Original: " + response);
        System.out.println("Filtered: " + result);
    }

    @Test
    public void testFilterDisplayFields_NestedData() {
        // 测试嵌套数据的显示字段过滤
        String response = "{\"result\":{\"user\":{\"profile\":{\"name\":\"John\",\"age\":25},\"contact\":{\"email\":\"<EMAIL>\",\"phone\":\"123456789\"}}}}";

        OutParam outParam = new OutParam();
        List<OutParam.ColumnParam> columnList = new ArrayList<>();

        // 配置显示字段：profile中的name和contact中的email
        OutParam.ColumnParam nameColumn = new OutParam.ColumnParam();
        nameColumn.setColumnName("name");
        nameColumn.setDisplayName("姓名");
        nameColumn.setLocation("result/user/profile");
        columnList.add(nameColumn);

        OutParam.ColumnParam emailColumn = new OutParam.ColumnParam();
        emailColumn.setColumnName("email");
        emailColumn.setDisplayName("邮箱");
        emailColumn.setLocation("result/user/contact");
        columnList.add(emailColumn);

        outParam.setColumnList(columnList);

        String result = ResponseConverter.filterDisplayFields(response, outParam);

        assertNotNull(result);
        // 验证结构化表格格式
        assertTrue(result.contains("\"head\""));
        assertTrue(result.contains("\"data\""));

        // 验证表头信息
        assertTrue(result.contains("\"dataIndex\":\"name\""));
        assertTrue(result.contains("\"title\":\"姓名\""));
        assertTrue(result.contains("\"dataIndex\":\"email\""));
        assertTrue(result.contains("\"title\":\"邮箱\""));

        // 验证数据行（使用columnName作为key）
        assertTrue(result.contains("\"name\":\"John\""));
        assertTrue(result.contains("\"email\":\"<EMAIL>\""));
        assertFalse(result.contains("\"age\""));
        assertFalse(result.contains("\"phone\""));

        System.out.println("Original: " + response);
        System.out.println("Filtered: " + result);
    }

    @Test
    public void testFilterDisplayFields_NoDisplayFields() {
        // 测试没有配置显示字段的情况
        String response = "{\"data\":{\"id\":1,\"name\":\"John\"}}";

        OutParam outParam = new OutParam();
        outParam.setColumnList(new ArrayList<>());

        String result = ResponseConverter.filterDisplayFields(response, outParam);

        assertEquals(response, result); // 应该返回原始数据
    }

    @Test
    public void testFilterDisplayFields_EmptyResponse() {
        // 测试空响应的情况
        String response = "";

        OutParam outParam = new OutParam();

        String result = ResponseConverter.filterDisplayFields(response, outParam);

        assertEquals("", result);
    }

    @Test
    public void testFilterDisplayFields_EmptyDisplayName() {
        // 测试displayName为空的情况（应该被过滤掉）
        String response = "{\"data\":{\"id\":1,\"name\":\"John\",\"age\":25}}";

        OutParam outParam = new OutParam();
        List<OutParam.ColumnParam> columnList = new ArrayList<>();

        OutParam.ColumnParam nameColumn = new OutParam.ColumnParam();
        nameColumn.setColumnName("name");
        nameColumn.setDisplayName(""); // 空的displayName，应该被过滤掉
        nameColumn.setLocation("data");
        columnList.add(nameColumn);

        OutParam.ColumnParam ageColumn = new OutParam.ColumnParam();
        ageColumn.setColumnName("age");
        ageColumn.setDisplayName("年龄"); // 有效的displayName
        ageColumn.setLocation("data");
        columnList.add(ageColumn);

        outParam.setColumnList(columnList);

        String result = ResponseConverter.filterDisplayFields(response, outParam);

        assertNotNull(result);
        // 验证结构化表格格式
        assertTrue(result.contains("\"head\""));
        assertTrue(result.contains("\"data\""));

        // 验证只有有效displayName的字段被包含
        assertFalse(result.contains("\"dataIndex\":\"name\""));  // name字段的displayName为空，应被过滤
        assertTrue(result.contains("\"dataIndex\":\"age\""));
        assertTrue(result.contains("\"title\":\"年龄\""));

        // 验证数据行
        assertFalse(result.contains("\"name\":"));
        assertTrue(result.contains("\"age\":\"25\""));
        assertFalse(result.contains("\"id\""));

        System.out.println("Original: " + response);
        System.out.println("Filtered: " + result);
    }

    @Test
    public void testFilterDisplayFields_StructuredTableFormat() {
        // 测试结构化表格格式输出
        String response = "{\"data\":[{\"id\":1,\"name\":\"John\",\"age\":25},{\"id\":2,\"name\":\"Jane\",\"age\":30}]}";

        OutParam outParam = new OutParam();
        List<OutParam.ColumnParam> columnList = new ArrayList<>();

        OutParam.ColumnParam nameColumn = new OutParam.ColumnParam();
        nameColumn.setColumnName("name");
        nameColumn.setDisplayName("姓名");
        nameColumn.setLocation("data");
        columnList.add(nameColumn);

        OutParam.ColumnParam ageColumn = new OutParam.ColumnParam();
        ageColumn.setColumnName("age");
        ageColumn.setDisplayName("年龄");
        ageColumn.setLocation("data");
        columnList.add(ageColumn);

        outParam.setColumnList(columnList);

        String result = ResponseConverter.filterDisplayFields(response, outParam);

        assertNotNull(result);
        // 验证结构化表格格式
        assertTrue(result.contains("\"head\""));
        assertTrue(result.contains("\"data\""));

        // 验证表头结构
        assertTrue(result.contains("\"dataIndex\":\"name\""));
        assertTrue(result.contains("\"title\":\"姓名\""));
        assertTrue(result.contains("\"key\":\"name\""));
        assertTrue(result.contains("\"dataIndex\":\"age\""));
        assertTrue(result.contains("\"title\":\"年龄\""));
        assertTrue(result.contains("\"key\":\"age\""));

        // 验证数据行（使用columnName作为key）
        assertTrue(result.contains("\"name\":\"John\""));
        assertTrue(result.contains("\"age\":\"25\""));
        assertTrue(result.contains("\"name\":\"Jane\""));
        assertTrue(result.contains("\"age\":\"30\""));
        assertFalse(result.contains("\"id\""));

        System.out.println("Original: " + response);
        System.out.println("Filtered (Structured Table Format): " + result);
    }

    @Test
    public void testFilterDisplayFields_SingleObjectStructured() {
        // 测试单个对象转换为结构化表格格式
        String response = "{\"user\":{\"name\":\"John\",\"email\":\"<EMAIL>\",\"age\":25}}";

        OutParam outParam = new OutParam();
        List<OutParam.ColumnParam> columnList = new ArrayList<>();

        OutParam.ColumnParam nameColumn = new OutParam.ColumnParam();
        nameColumn.setColumnName("name");
        nameColumn.setDisplayName("姓名");
        nameColumn.setLocation("user");
        columnList.add(nameColumn);

        OutParam.ColumnParam emailColumn = new OutParam.ColumnParam();
        emailColumn.setColumnName("email");
        emailColumn.setDisplayName("邮箱");
        emailColumn.setLocation("user");
        columnList.add(emailColumn);

        outParam.setColumnList(columnList);

        String result = ResponseConverter.filterDisplayFields(response, outParam);

        assertNotNull(result);
        // 验证结构化表格格式
        assertTrue(result.contains("\"head\""));
        assertTrue(result.contains("\"data\""));

        // 验证表头信息
        assertTrue(result.contains("\"dataIndex\":\"name\""));
        assertTrue(result.contains("\"title\":\"姓名\""));
        assertTrue(result.contains("\"dataIndex\":\"email\""));
        assertTrue(result.contains("\"title\":\"邮箱\""));

        // 验证数据行（单个对象应该产生一行数据）
        assertTrue(result.contains("\"name\":\"John\""));
        assertTrue(result.contains("\"email\":\"<EMAIL>\""));
        assertFalse(result.contains("\"age\""));

        System.out.println("Original: " + response);
        System.out.println("Filtered (Single Object Structured): " + result);
    }

    @Test
    public void testFilterDisplayFields_CompleteExample() {
        // 完整示例测试，验证输出格式完全符合要求
        String response = "{\"users\":[{\"name\":\"John\",\"age\":25},{\"name\":\"Jane\",\"age\":30}]}";

        OutParam outParam = new OutParam();
        List<OutParam.ColumnParam> columnList = new ArrayList<>();

        OutParam.ColumnParam nameColumn = new OutParam.ColumnParam();
        nameColumn.setColumnName("name");
        nameColumn.setDisplayName("姓名");
        nameColumn.setLocation("users");
        columnList.add(nameColumn);

        OutParam.ColumnParam ageColumn = new OutParam.ColumnParam();
        ageColumn.setColumnName("age");
        ageColumn.setDisplayName("年龄");
        ageColumn.setLocation("users");
        columnList.add(ageColumn);

        outParam.setColumnList(columnList);

        String result = ResponseConverter.filterDisplayFields(response, outParam);

        assertNotNull(result);

        // 验证完整的结构化格式
        String expectedPattern = ".*\"head\":\\[.*\"dataIndex\":\"name\".*\"title\":\"姓名\".*\"key\":\"name\".*\"dataIndex\":\"age\".*\"title\":\"年龄\".*\"key\":\"age\".*\\].*\"data\":\\[.*\"name\":\"John\".*\"age\":\"25\".*\"name\":\"Jane\".*\"age\":\"30\".*\\].*";
        assertTrue(result.matches(expectedPattern.replace(".*", ".*?")));

        System.out.println("Complete Example Result: " + result);

        // 验证JSON格式有效性
        try {
            JSONUtil.parse(result);
            System.out.println("✓ JSON格式验证通过");
        } catch (Exception e) {
            fail("生成的JSON格式无效: " + e.getMessage());
        }
    }
}
