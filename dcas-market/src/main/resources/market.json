{"valid": true, "violations": [], "id": "dcas_market", "icon": "http://*************:1008/icon/logo.png", "lastModified": 1715684167, "name": "dcas_market", "title": "DCAS能力市场", "apps": [{"valid": true, "buildIn": false, "violations": [], "id": "dcas-assistant", "lastModified": 1715684167, "icon": "http://*************:1008/icon/ai.png", "readMe": "## dcas-assistant\n\ndcas-assistant内置智能分析模型", "description": "内置智能分析模型", "name": "dcas-assistant", "tags": ["AI / 大模型"], "title": "内置智能分析模型", "additionalProperties": {"key": "dcas-assistant", "name": "dcas-assistant", "tags": ["AI"], "shortDescZh": "内置智能分析模型", "shortDescEn": "llama3-based AI Assistant", "type": "tool", "crossVersionUpdate": false, "limit": 1, "recommend": 0, "github": "https://github.com/zfile-dev/zfile", "document": "https://docs.zfile.vip/"}, "versions": [{"valid": true, "violations": [], "id": "2-0-0", "readMe": null, "name": "2.0.0", "lastModified": 1715684167, "files": [{"name": "dcas-assistant-2.0.0.tar.gz", "size": 7745, "lastModified": "2024-05-14T10:56:07.000+00:00"}], "downloadUrl": "http://*************:1008/dcas-assistant-2.0.0.tar.gz", "downloadCallbackUrl": "http://*************:1008/dcas-assistant/versions/2-0-0/download-callback?t=1715684167", "additionalProperties": {"formFields": [{"defaultValue": 8000, "edit": false, "envKey": "DCAS_APP_PORT_HTTP", "labelEn": "Port", "labelZh": "端口", "required": true, "rule": "paramPort", "type": "number"}]}}], "downloads": 0, "downloadUrl": "http://*************:1008/dcas-assistant-2.0.0.tar.gz", "downloadCallbackUrl": "http://*************:1008/dcas-assistant/versions/2-0-0/download-callback?t=1715684167"}, {"valid": true, "buildIn": false, "violations": [], "id": "dcas-compare", "lastModified": 1715684167, "icon": "http://*************:1008/icon/compare.png", "readMe": "# DCAS-COMPARE 脱敏加密检测服务\n\n&emsp;&emsp;", "description": "脱敏加密检测服务", "name": "dcas-compare", "tags": ["本地"], "title": "脱敏加密检测服务", "additionalProperties": {"key": "dcas-compare", "name": "dcas-compare", "tags": ["Local"], "shortDescZh": "脱敏加密检测服务", "shortDescEn": "Desensitization encryption detection", "type": "tool", "crossVersionUpdate": false, "limit": 1, "recommend": 0, "website": "https://${ip}:4433/dcas-compare/", "github": "", "document": ""}, "versions": [{"valid": true, "violations": [], "id": "2-0-0", "readMe": null, "name": "2.0.0", "lastModified": 1715684167, "files": [{"name": "dcas-compare-2.0.0.tar.gz", "size": 7745, "lastModified": "2024-05-14T10:56:07.000+00:00"}], "downloadUrl": "http://*************:1008/dcas-compare-2.0.0.tar.gz", "downloadCallbackUrl": "http://*************:1008/dcas-compare/versions/2-0-0/download-callback?t=1715684167", "additionalProperties": {"formFields": [{"defaultValue": 8083, "edit": false, "envKey": "DCAS_APP_PORT_HTTP", "labelEn": "Port", "labelZh": "端口", "required": true, "rule": "paramPort", "type": "number"}]}}], "downloads": 0, "downloadUrl": "http://*************:1008/dcas-compare-2.0.0.tar.gz", "downloadCallbackUrl": "http://*************:1008/dcas-compare/versions/2-0-0/download-callback?t=1715684167"}, {"valid": true, "buildIn": false, "violations": [], "id": "dcas-pcap", "lastModified": 1724135568, "icon": "http://*************:1008/icon/pcap.png", "readMe": "# DCAS-PCAP 网络流量发现组件\n\n&emsp;&emsp;", "description": "网络流量发现组件", "name": "dcas-pcap", "tags": ["本地"], "title": "网络流量发现组件", "additionalProperties": {"key": "dcas-pcap", "name": "dcas-pcap", "tags": ["Local"], "shortDescZh": "网络流量发现组件", "shortDescEn": "Network traffic discovery component", "type": "tool", "crossVersionUpdate": false, "limit": 1, "recommend": 0, "website": "https://${ip}:4433/pcap/", "github": "", "document": ""}, "versions": [{"valid": true, "violations": [], "id": "2-0-0", "readMe": null, "name": "2.0.0", "lastModified": 1724135568, "files": [{"name": "dcas-pcap-2.0.0.tar.gz", "size": 8820, "lastModified": "2024-08-20T14:36:00.000+00:00"}], "downloadUrl": "http://*************:1008/dcas-pcap-2.0.0.tar.gz", "downloadCallbackUrl": "http://*************:1008/dcas-pcap/versions/2-0-0/download-callback?t=1724135568", "additionalProperties": {"formFields": [{"defaultValue": 8090, "edit": false, "envKey": "DCAS_APP_PORT_HTTP", "labelEn": "Port", "labelZh": "端口", "required": true, "rule": "paramPort", "type": "number"}]}}], "downloads": 0, "downloadUrl": "http://*************:1008/dcas-pcap-2.0.0.tar.gz", "downloadCallbackUrl": "http://*************:1008/dcas-pcap/versions/2-0-0/download-callback?t=1715684167"}, {"valid": true, "buildIn": true, "violations": [], "id": "dcas-authority", "lastModified": 1715684167, "icon": "http://*************:1008/icon/auth.png", "readMe": "# DCAS-AUTHORITY 访问权限检测服务\n\n&emsp;&emsp;", "description": "访问权限检测服务", "name": "dcas-authority", "tags": ["本地"], "title": "访问权限检测服务", "additionalProperties": {"key": "dcas-authority", "name": "dcas-authority", "tags": ["Local"], "shortDescZh": "访问权限检测服务", "shortDescEn": "Access permission detection service", "type": "tool", "crossVersionUpdate": false, "limit": 1, "recommend": 0, "website": "https://${ip}:4433/#/accessPermissions/", "github": "", "document": ""}, "versions": [{"valid": true, "violations": [], "id": "1-0-0", "readMe": null, "name": "1.0.0", "lastModified": 1715684167, "files": [{"name": "dcas-authority-1.0.0.tar.gz", "size": 7745, "lastModified": "2024-05-14T10:56:07.000+00:00"}], "downloadUrl": "", "downloadCallbackUrl": "", "additionalProperties": {"formFields": [{"defaultValue": 8088, "edit": false, "envKey": "DCAS_APP_PORT_HTTP", "labelEn": "Port", "labelZh": "端口", "required": true, "rule": "paramPort", "type": "number"}]}}], "downloads": 0, "downloadUrl": "", "downloadCallbackUrl": ""}, {"valid": true, "buildIn": true, "violations": [], "id": "dcas-capability", "lastModified": 1715684167, "icon": "http://*************:1008/icon/auth.png", "readMe": "# DCAS-CAPABILITY 数据安全能力验证\n\n&emsp;&emsp;", "description": "数据安全能力验证", "name": "dcas-capability", "tags": ["本地"], "title": "数据安全能力验证", "additionalProperties": {"key": "dcas-capability", "name": "dcas-capability", "tags": ["Local"], "shortDescZh": "数据安全能力验证", "shortDescEn": "Access permission detection service", "type": "tool", "crossVersionUpdate": false, "limit": 1, "recommend": 0, "website": "https://${ip}:4433/#/capabilityVerification/", "github": "", "document": ""}, "versions": [{"valid": true, "violations": [], "id": "1-0-0", "readMe": null, "name": "1.0.0", "lastModified": 1715684167, "files": [{"name": "dcas-capability-1.0.0.tar.gz", "size": 7745, "lastModified": "2024-05-14T10:56:07.000+00:00"}], "downloadUrl": "", "downloadCallbackUrl": "", "additionalProperties": {"formFields": [{"defaultValue": 8089, "edit": false, "envKey": "DCAS_APP_PORT_HTTP", "labelEn": "Port", "labelZh": "端口", "required": true, "rule": "paramPort", "type": "number"}]}}], "downloads": 0, "downloadUrl": "", "downloadCallbackUrl": ""}], "additionalProperties": {"tags": [{"key": "WebSite", "name": "建站", "sort": 1}, {"key": "Database", "name": "数据库", "sort": 2}, {"key": "Server", "name": "Web 服务器", "sort": 3}, {"key": "Runtime", "name": "运行环境", "sort": 4}, {"key": "Tool", "name": "实用工具", "sort": 5}, {"key": "Storage", "name": "云存储", "sort": 6}, {"key": "AI", "name": "AI / 大模型", "sort": 7}, {"key": "BI", "name": "BI", "sort": 8}, {"key": "Security", "name": "安全", "sort": 9}, {"key": "DevTool", "name": "开发工具", "sort": 10}, {"key": "DevOps", "name": "DevOps", "sort": 11}, {"key": "Middleware", "name": "中间件", "sort": 12}, {"key": "Media", "name": "多媒体", "sort": 13}, {"key": "Email", "name": "邮件服务", "sort": 14}, {"key": "Game", "name": "休闲游戏", "sort": 15}, {"key": "Local", "name": "本地", "sort": 99}], "version": "v1.0.0"}}