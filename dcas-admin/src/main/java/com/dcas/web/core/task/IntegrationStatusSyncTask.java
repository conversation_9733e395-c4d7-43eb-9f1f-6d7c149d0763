package com.dcas.web.core.task;

import cn.hutool.core.collection.CollUtil;
import com.dcas.common.domain.entity.ApiIntegratedProduct;
import com.dcas.common.domain.entity.ApiInterface;
import com.dcas.common.domain.entity.ApiRelease;
import com.dcas.common.enums.IntegrationStatusEnum;
import com.dcas.common.model.vo.ApiReleaseVO;
import com.dcas.common.model.vo.ProductInfoVO;
import com.dcas.common.model.vo.ProductIntegrationVO;
import com.dcas.common.utils.PartitionUtils;
import com.dcas.system.service.IApiCallService;
import com.dcas.system.service.IApiReleaseService;
import com.dcas.system.service.impl.ApiIntegratedProductServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 集成状态同步定时任务
 * 定期检查和更新第三方产品集成的状态
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class IntegrationStatusSyncTask {

    private final IApiCallService apiCallService;
    private final IApiReleaseService apiReleaseService;
    private final ApiIntegratedProductServiceImpl apiIntegratedProductService;

    /**
     * 每天零点执行一次集成状态同步
     * 检查集成产品的连接状态、参数更新状态等
     */
    @Scheduled(cron = "0 0 0 * * ?")
    public void syncIntegrationStatus() {
        try {
            log.info("开始执行集成状态同步任务");
            // 查询最新版本的产品
            List<ApiReleaseVO> apiReleases = apiReleaseService.queryApiReleaseList();
            Map<Long, ApiReleaseVO> releaseVersion = apiReleases.stream().collect(Collectors.toMap(ApiRelease::getId, Function.identity()));
            List<ProductIntegrationVO> integrationPage = apiIntegratedProductService.getIntegrationPage(null);
            List<ProductInfoVO> productInfoList = integrationPage.stream().flatMap(i -> i.getProducts().stream()).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(productInfoList)) {
                List<ApiIntegratedProduct> list = new ArrayList<>();
                for (ProductInfoVO productInfoVO : productInfoList) {
                    ApiIntegratedProduct apiIntegratedProduct = new ApiIntegratedProduct();
                    apiIntegratedProduct.setId(apiIntegratedProduct.getId());
                    apiIntegratedProduct.setStatus(IntegrationStatusEnum.NORMAL_CONNECTION.getCode());
                    ApiReleaseVO apiReleaseVO = releaseVersion.get(productInfoVO.getId());
                    if (Objects.isNull(apiReleaseVO)) {
                        apiIntegratedProduct.setStatus(IntegrationStatusEnum.DELETED_FROM_KNOWLEDGE_BASE.getCode());
                        continue;
                    }
                    Integer newVersion = apiReleaseVO.getVersion();
                    if (newVersion > productInfoVO.getVersion()) {
                        apiIntegratedProduct.setStatus(IntegrationStatusEnum.PARAMETER_UPDATED.getCode());
                        apiIntegratedProduct.setVersion(newVersion);
                        continue;
                    }
                    List<ApiInterface> apiInterfaces = apiReleaseVO.getApiInterfaces();
                    if (CollUtil.isEmpty(apiInterfaces)) {
                        apiIntegratedProduct.setStatus(IntegrationStatusEnum.CONNECTION_ERROR.getCode());
                        continue;
                    }
                    boolean response = apiCallService.testApiConnection(apiInterfaces.get(0), productInfoVO.getParams(), productInfoVO.getUrl());
                    if (!response) {
                        apiIntegratedProduct.setStatus(IntegrationStatusEnum.CONNECTION_ERROR.getCode());
                    }
                    list.add(apiIntegratedProduct);
                }
                PartitionUtils.part(list, apiIntegratedProductService::updateBatchById);
            }
        } catch (Exception e) {
            log.error("集成状态同步任务执行异常", e);
        }
    }
}
