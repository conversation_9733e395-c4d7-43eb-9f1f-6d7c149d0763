package com.dcas.web.core.runner;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.dcas.common.enums.AttributeEnum;
import com.dcas.common.enums.SysConfigEnum;
import com.dcas.common.enums.SysEditionEnum;
import com.dcas.common.domain.entity.CoLicense;
import com.dcas.common.domain.entity.SysConfig;
import com.dcas.common.model.vo.QueryLicenseVo;
import com.dcas.system.service.CoLicenseService;
import com.dcas.system.service.ISysConfigService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.temporal.TemporalField;
import java.util.concurrent.TimeUnit;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date 2023/1/10 17:09
 * @since 1.0.1
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class LicenseAdvanceRunner implements ApplicationRunner {

    private final CoLicenseService coLicenseService;
    private final ISysConfigService sysConfigService;

    /*
     * 可能在容器中部署，每次启动需要更新机器码
     */
    @Override
    public void run(ApplicationArguments args) {
        // 终端识别码
        SysConfig terminalCode = sysConfigService.selectConfigById(SysConfigEnum.TERMINAL_CODE.getCode());
        if (StrUtil.isEmpty(terminalCode.getConfigValue())) {
            terminalCode.setConfigValue(UUID.randomUUID().toString());
            sysConfigService.updateConfig(terminalCode);
        }
        SysConfig config = sysConfigService.selectConfigById(SysConfigEnum.MACHINE_CODE.getCode());
        QueryLicenseVo license = null;
        try {
            license = coLicenseService.queryLicense(null);
        } catch (Exception e) {
            log.error("统一license服务异常：{}", e.getMessage());
            return;
        }
        config.setConfigValue(license.getDeviceId());
        sysConfigService.updateConfig(config);

        // 系统配置表增加标准版标识
        if (sysConfigService.count(SysConfigEnum.SYSTEM_EDITION.getKey()) == 0){
            log.info("update system edition: {}", SysEditionEnum.SE);
            sysConfigService.saveOrUpdateSysEdition(SysEditionEnum.SE.getEdition(), "dcasadmin");
        }
    }
}
