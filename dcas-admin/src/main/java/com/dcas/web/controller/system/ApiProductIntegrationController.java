package com.dcas.web.controller.system;

import com.dcas.common.annotation.Log;
import com.dcas.common.core.domain.R;
import com.dcas.common.enums.BusinessType;
import com.dcas.common.enums.LogType;
import com.dcas.common.model.req.CreateIntegrationReq;
import com.dcas.common.model.req.IdsReq;
import com.dcas.common.model.req.UpdateIntegrationReq;
import com.dcas.common.model.vo.*;
import com.dcas.system.service.IApiReleaseService;
import com.dcas.system.service.ApiIntegratedProductService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 产品对接管理
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/integration")
@Api(tags = "产品对接管理")
@RequiredArgsConstructor
public class ApiProductIntegrationController {

    private final ApiIntegratedProductService integratedProductService;
    private final IApiReleaseService apiReleaseService;

    /**
     * 查询产品能力树形结构
     */
    @GetMapping("/capability/tree")
    @ApiOperation("查询产品能力树形结构")
    public R<List<ProductCapabilityTreeVO>> getCapabilityTree() {
        List<ProductCapabilityTreeVO> tree = apiReleaseService.getProductCapabilityTree();
        return R.success(tree);
    }

    /**
     * 查询产品授权参数
     * @param releaseId 产品类型id
     * @return 授权参数列表
     */
    @GetMapping("/products/header/{releaseId}")
    @ApiOperation("查询产品授权参数")
    public R<List<IntegrationFormFieldVO>> getProductHeader(@ApiParam("产品类型id") @PathVariable("releaseId") Long releaseId) {
        return R.success(apiReleaseService.getProductHeader(releaseId));
    }

    /**
     * 根据选择产品类型查询对接该产品配置的接口参数
     */
    @GetMapping("/products/{productId}")
    @ApiOperation("根据能力查询产品接口参数")
    public R<List<IntegrationInterfaceVO>> getProductsByCapability(@ApiParam("对接产品id") @PathVariable("productId") Long productId) {
        return R.success(apiReleaseService.getProductsByReleaseId(productId));
    }

    /**
     * 查询集成产品列表
     */
    @GetMapping("/list")
    @ApiOperation("查询集成产品列表")
    public R<List<ProductIntegrationVO>> getIntegrationList(String capability) {
        return R.success(integratedProductService.getIntegrationPage(capability));
    }

    @GetMapping("/product/{id}")
    @ApiOperation("查询产品详情")
    public R<ProductInfoVO> getProductInfo(@ApiParam("产品id") @PathVariable("id") Long id) {
        return R.success(integratedProductService.getProductInfo(id));
    }

    /**
     * 创建新的产品集成
     */
    @Log(title = "创建产品集成", businessType = BusinessType.INSERT, logType = LogType.OPERATE, module = "产品集成管理")
    @PostMapping
    @ApiOperation("创建新的产品集成")
    public R<Boolean> createIntegration(@Validated @RequestBody CreateIntegrationReq request) {
        integratedProductService.createIntegration(request);
        return R.success();
    }

    /**
     * 更新产品集成
     */
    @Log(title = "更新产品集成", businessType = BusinessType.UPDATE, logType = LogType.OPERATE, module = "产品集成管理")
    @PutMapping
    @ApiOperation("更新产品集成")
    public R<Boolean> updateIntegration(@Validated @RequestBody UpdateIntegrationReq request) {
        integratedProductService.updateIntegration(request);
        return R.success();
    }

    /**
     * 删除产品集成
     */
    @Log(title = "删除产品集成", businessType = BusinessType.DELETE, logType = LogType.OPERATE, module = "产品集成管理")
    @PostMapping("/batchDelete")
    @ApiOperation("删除产品集成")
    public R<Boolean> deleteIntegrations(@Validated @RequestBody IdsReq ids) {
        integratedProductService.deleteIntegrations(ids);
        return R.success();
    }

}
