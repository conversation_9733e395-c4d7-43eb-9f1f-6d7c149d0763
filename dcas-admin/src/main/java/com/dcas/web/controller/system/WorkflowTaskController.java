package com.dcas.web.controller.system;

import com.dcas.common.annotation.Log;
import com.dcas.common.core.domain.R;
import com.dcas.common.domain.entity.WorkflowTask;
import com.dcas.common.enums.BusinessType;
import com.dcas.common.enums.LogType;
import com.dcas.common.model.req.IdsReq;
import com.dcas.common.model.req.WorkflowTaskReq;
import com.dcas.common.model.req.WorkflowTaskUpdateReq;
import com.dcas.common.model.vo.WorkflowDetailVO;
import com.dcas.common.model.vo.WorkflowTaskDetailVO;
import com.dcas.common.utils.PageResult;
import com.dcas.system.service.IWorkflowTaskService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.concurrent.CompletableFuture;

/**
 * 工作流任务Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/workflow/task")
@Api(tags = "工作流任务管理")
@RequiredArgsConstructor
public class WorkflowTaskController {

    private final IWorkflowTaskService workflowTaskService;

    /**
     * 查询工作流任务列表
     */
    @GetMapping("/list")
    @ApiOperation("查询工作流任务列表")
    public R<PageResult<WorkflowTask>> list(WorkflowTaskReq req) {
        return R.success(workflowTaskService.selectWorkflowTaskList(req));
    }

    @GetMapping("/detail")
    @ApiOperation("查询工作流任务详情")
    public R<WorkflowDetailVO> detail(@RequestParam("taskId") Long taskId) {
        return R.success(workflowTaskService.detail(taskId));
    }

    /**
     * 获取工作流任务详细信息
     */
    @GetMapping(value = "/{id}")
    @ApiOperation("获取工作流任务详细信息")
    public R<WorkflowTaskDetailVO> getInfo(@ApiParam("任务ID") @PathVariable("id") Long id) {
        return R.success(workflowTaskService.selectWorkflowTaskByTaskId(id));
    }

    /**
     * 新增工作流任务
     */
    @Log(title = "新增工作流任务", businessType = BusinessType.INSERT, logType = LogType.OPERATE, module = "工作流任务管理")
    @PostMapping
    @ApiOperation("新增工作流任务")
    public R<Boolean> add(@Validated @RequestBody WorkflowTask workflowTask) {
        workflowTaskService.insertWorkflowTask(workflowTask);
        return R.success();
    }

    /**
     * 修改工作流任务
     */
    @Log(title = "修改工作流任务", businessType = BusinessType.UPDATE, logType = LogType.OPERATE, module = "工作流任务管理")
    @PutMapping
    @ApiOperation("修改工作流任务")
    public R<Boolean> edit(@Validated @RequestBody WorkflowTaskUpdateReq req) {
        workflowTaskService.updateWorkflowTask(req);
        return R.success();
    }

    /**
     * 删除工作流任务
     */
    @Log(title = "批量删除工作流任务", businessType = BusinessType.DELETE, logType = LogType.OPERATE, module = "工作流任务管理")
    @PostMapping("/batchDelete")
    @ApiOperation("批量删除工作流任务")
    public R<Boolean> remove(@ApiParam("任务ID列表") @RequestBody IdsReq req) {
        workflowTaskService.deleteWorkflowTaskByTaskIds(req);
        return R.success();
    }

    /**
     * 执行工作流任务
     */
    @Log(title = "执行工作流任务", businessType = BusinessType.OTHER, logType = LogType.OPERATE, module = "工作流任务管理")
    @PostMapping("/execute")
    @ApiOperation("执行工作流任务")
    public R<String> execute(@ApiParam("任务ID") Long taskId) {
        CompletableFuture<Void> future = workflowTaskService.executeTaskAsync(taskId);
        return R.success("任务已开始执行");
    }

    /**
     * 终止工作流任务
     */
    /*@Log(title = "终止工作流任务", businessType = BusinessType.OTHER, logType = LogType.OPERATE, module = "工作流任务管理")
    @PostMapping("/{taskId}/terminate")
    @ApiOperation("终止工作流任务")
    public R<Void> terminate(@ApiParam("任务ID") @PathVariable("taskId") Long taskId,
                                   @ApiParam("终止原因") @RequestParam(required = false, defaultValue = "手动终止") String reason) {
        if (!workflowTaskService.canTerminateTask(taskId)) {
            return R.fail("任务当前状态不允许终止");
        }

        boolean success = workflowTaskService.terminateTask(taskId, reason);
        return success ? R.success() : R.fail();
    }*/
}
