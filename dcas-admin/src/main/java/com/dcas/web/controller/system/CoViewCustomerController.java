package com.dcas.web.controller.system;

import com.dcas.common.core.domain.R;
import com.dcas.common.core.domain.RequestModel;
import com.dcas.common.core.domain.ResponseApi;
import com.dcas.common.model.dto.*;
import com.dcas.common.domain.entity.CoGapAnalysis;
import com.dcas.common.model.vo.*;
import com.dcas.system.service.CoLicenseService;
import com.dcas.system.service.CoViewCustomerService;
import com.dcas.system.service.CommonService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 工作视图模块控制层
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping(value = "/api/customer/view")
@Api(tags = "客户视图")
@RequiredArgsConstructor
public class CoViewCustomerController {

    private final CoViewCustomerService coViewCustomerService;

    /**
     * 查询客户业务情况
     */
    @ApiOperation(value = "查询客户业务情况")
    @PostMapping(value = "/retrieveStatus")
    public ResponseApi<QueryStatusView> retrieveStatus(@RequestBody RequestModel<CustomerIdDto> dto) {
        try {
            QueryStatusView vo = coViewCustomerService.retrieveStatus(dto);
            return ResponseApi.ok(vo);
        } catch (Exception e) {  //其他异常
            log.info("查询客户业务情况失败!", e);
            return ResponseApi.fail();
        }
    }

    /**
     * 查询作业列表
     */
    @ApiOperation(value = "查询作业列表")
    @PostMapping(value = "/retrieveOperation")
    public ResponseApi<List<QueryOperationView>> selectOperationList(@RequestBody RequestModel<QueryOperationViewDto> dto) {
        try {
            List<QueryOperationView> list = coViewCustomerService.selectOperationList(dto);
            return ResponseApi.ok(list);
        } catch (Exception e) {  //捕捉入参校验异常
            return ResponseApi.fail("查询作业列表失败");
        }

    }

    /**
     * 数据资产分析
     */
    @ApiOperation(value = "数据资产分析")
    @PostMapping(value = "/operationReport/dataAssetsAnalysis")
    public ResponseApi<QueryDataAssetAnalysisVo> dataAssetsAnalysis(@RequestBody RequestModel<PrimaryKeyDTO> dto) {
        try {
            QueryDataAssetAnalysisVo vo = coViewCustomerService.queryDataAssetsAnalysis(dto);
            return ResponseApi.ok(vo);
        } catch (Exception e) {  //捕捉入参校验异常
            return ResponseApi.fail("数据资产分析失败");
        }

    }

    /**
     * 权限现状分析
     */
    @ApiOperation(value = "权限现状分析")
    @PostMapping(value = "/operationReport/authorityAnalysis")
    public ResponseApi<ReportAuthorityVo> authorityAnalysis(@RequestBody RequestModel<QueryAuthorityReportDto> dto) {
        try {
            ReportAuthorityVo vo = coViewCustomerService.authorityAnalysis(dto);
            return ResponseApi.ok(vo);
        } catch (Exception e) {  //捕捉入参校验异常
            return ResponseApi.fail("权限现状分析失败");
        }//其他异常

    }

    @ApiOperation(value = "权限现状分析-查询用户权限总计")
    @PostMapping(value = "/operationReport/userAuthResult")
    public ResponseApi<List<ReportAuthorityStatusQuoVo>> queryUserAuthResult(@RequestBody RequestModel<OperationIdDto> dto) {
        return ResponseApi.ok(coViewCustomerService.queryUserAuthResult(dto));
    }

    @ApiOperation(value = "权限现状分析-查询用户权限分类统计结果")
    @PostMapping(value = "/operationReport/userAuthType")
    public ResponseApi<PageInfo<ReportAuthorityByUserVo>> queryUserAuthType(@RequestBody RequestModel<QueryAuthorityReportDto> dto) {

        return ResponseApi.ok(coViewCustomerService.queryUserAuthType(dto));
    }

    @ApiOperation(value = "权限现状分析-查询用户权限资产数量一览")
    @PostMapping(value = "/operationReport/userAssetResult")
    public ResponseApi<List<ReportAuthorityUserAssetVo>> queryUserAssetResult(@RequestBody RequestModel<OperationIdDto> dto) {
        return ResponseApi.ok(coViewCustomerService.queryUserAssetResult(dto));
    }


    @ApiOperation(value = " 能力差距分析--查询图表名称")
    @PostMapping(value = "/operationReport/gapAnalysis/queryChartName")
    public ResponseApi<List<String>> queryChartName(@RequestBody RequestModel<OperationIdDto> dto) {
        try {
            List<String> list = coViewCustomerService.queryChartName(dto.getPrivator());
            return ResponseApi.ok(list);
        } catch (Exception e) {  //捕捉入参校验异常
            return ResponseApi.fail("能力差距分析--查询图表名称失败");
        }

    }

    /**
     * 能力差距分析--查询分析图表
     */
    @ApiOperation(value = " 能力差距分析--查询分析图表")
    @PostMapping(value = "/operationReport/gapAnalysis/queryChart")
    public ResponseApi<List<CoGapAnalysis>> queryGapAnalysisChart(@RequestBody RequestModel<QueryGapAnalysisChartViewDto> dto) {
        try {
            List<CoGapAnalysis> list = coViewCustomerService.queryGapAnalysisChart(dto);
            return ResponseApi.ok(list);
        } catch (Exception e) {  //捕捉入参校验异常
            return ResponseApi.fail("能力差距分析--查询分析图表失败");
        }

    }

    /**
     * 能力差距分析--符合情况分析
     */
    @ApiOperation(value = " 能力差距分析--符合情况分析")
    @PostMapping(value = "/operationReport/gapAnalysis/queryGapConformResult")
    public ResponseApi<QueryViewGapAnalysisConformVo> queryGapConformResult(@RequestBody RequestModel<QueryGapAnalysisChartViewDto> dto) {
        try {
            QueryViewGapAnalysisConformVo vo = coViewCustomerService.queryGapConformResult(dto);
            return ResponseApi.ok(vo);
        } catch (Exception e) {  //捕捉入参校验异常
            return ResponseApi.fail("能力差距分析--符合情况分析失败");
        }

    }

    /**
     * 基础环境风险分析--查询漏洞比例
     */
    @ApiOperation(value = "基础环境风险分析--查询漏洞比例")
    @PostMapping(value = "/operationReport/loopholeAnalysis/queryLoopholeFactor")
    public ResponseApi<QueryViewLoopholeFactorVo> queryLoopholeFactor(@RequestBody RequestModel<CommonDto> dto) {
        try {
            QueryViewLoopholeFactorVo vo = coViewCustomerService.queryLoopholeFactor(dto);
            return ResponseApi.ok(vo);
        } catch (Exception e) {  //捕捉入参校验异常
            return ResponseApi.fail("基础环境风险分析--查询漏洞比例失败");
        }

    }

    /**
     * 基础环境风险分析--按数据库分析结果
     */
    @ApiOperation(value = "基础环境风险分析--按数据库分析结果")
    @PostMapping(value = "/operationReport/loopholeAnalysis/queryLoopholeByDatabase")
    public ResponseApi<PageInfo<QueryViewLoopholeByDatabaseVo>> queryLoopholeByDatabase(@RequestBody RequestModel<CommonDto> dto) {
        try {
            //分页
            PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
            //查询结果
            List<QueryViewLoopholeByDatabaseVo> voList = coViewCustomerService.queryLoopholeByDatabase(dto);
            //设置分页条件，根据list分页
            PageInfo<QueryViewLoopholeByDatabaseVo> info = new PageInfo<>(voList);
            return ResponseApi.ok(info);
        } catch (Exception e) {  //捕捉入参校验异常
            return ResponseApi.fail("基础环境风险分析--按数据库分析结果失败");
        }

    }

    /**
     * 基础环境风险分析--数据库主机漏洞分析结果
     */
    @ApiOperation(value = "基础环境风险分析--数据库主机漏洞分析结果")
    @PostMapping(value = "/operationReport/loopholeAnalysis/queryLoopholeBySystem")
    public ResponseApi<PageInfo<QueryViewLoopholeBySystemVo>> queryLoopholeBySystem(@RequestBody RequestModel<CommonDto> dto) {
        try {
            //分页
            PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
            //查询结果
            List<QueryViewLoopholeBySystemVo> voList = coViewCustomerService.queryLoopholeBySystem(dto);
            //设置分页条件，根据list分页
            PageInfo<QueryViewLoopholeBySystemVo> info = new PageInfo<>(voList);
            return ResponseApi.ok(info);
        } catch (Exception e) {  //捕捉入参校验异常
            return ResponseApi.fail("基础环境风险分析--数据库主机漏洞分析结果失败");
        }

    }

    /**
     * 合规风险分析--比例
     */
    @ApiOperation(value = "合规风险分析--比例")
    @PostMapping(value = "/operationReport/legalAnalysis/queryLegalProportion")
    public ResponseApi<QueryViewLegalResultVo> queryLegalProportion(@RequestBody RequestModel<OperationIdDto> dto) {
        try {
            QueryViewLegalResultVo vo = coViewCustomerService.queryLegalProportion(dto);
            return ResponseApi.ok(vo);
        } catch (Exception e) {  //捕捉入参校验异常
            return ResponseApi.fail("合规风险分析失败");
        }//其他异常

    }

    /**
     * 合规风险分析--合规结果分布一览
     */
    @ApiOperation(value = "合规风险分析--合规结果分布一览")
    @PostMapping(value = "/operationReport/legalAnalysis/queryLegalResult")
    public ResponseApi<List<QueryViewLegalResultVo>> queryLegalResult(@RequestBody RequestModel<OperationIdDto> dto) {
        try {
            List<QueryViewLegalResultVo> voList = coViewCustomerService.queryLegalResult(dto);
            return ResponseApi.ok(voList);
        } catch (Exception e) {  //捕捉入参校验异常
            return ResponseApi.fail("合规风险分析--合规结果分布一览失败");
        }

    }

    /**
     * 业务系统风险分析一览
     */
    @ApiOperation(value = "业务系统风险分析一览")
    @PostMapping(value = "/operationReport/legalAnalysis/queryLifecycleResult")
    public ResponseApi<QueryViewLifecycleSystemResultVo> queryLifecycleResult(@RequestBody RequestModel<OperationIdDto> dto) {
        try {
            QueryViewLifecycleSystemResultVo vo = coViewCustomerService.queryLifecycleResult(dto);
            return ResponseApi.ok(vo);
        } catch (Exception e) {  //捕捉入参校验异常
            return ResponseApi.fail("业务系统风险分析一览失败");
        }

    }


    /**
     * 业务系统风险排序
     */
    @ApiOperation(value = "业务系统风险排序")
    @PostMapping(value = "/operationReport/legalAnalysis/queryLifecycleResultList")
    public ResponseApi<List<QueryViewLifecycleSystemResultVo>> queryLifecycleResultList(@RequestBody RequestModel<OperationIdDto> dto) {
        try {
            List<QueryViewLifecycleSystemResultVo> voList = coViewCustomerService.queryLifecycleResultList(dto);
            return ResponseApi.ok(voList);
        } catch (Exception e) {  //捕捉入参校验异常
            return ResponseApi.fail("业务系统风险排序失败");
        }

    }

    /**
     * 业务系统风险分析
     */
    @ApiOperation(value = "业务系统风险分析")
    @PostMapping(value = "/operationReport/legalAnalysis/queryLifecycleAnalysis")
    public ResponseApi<QueryViewLifecycleAnalysisVo> queryLifecycleAnalysis(@RequestBody RequestModel<QueryViewLifecycleAnalysisDto> dto) {
        try {
            QueryViewLifecycleAnalysisVo vo = coViewCustomerService.queryLifecycleAnalysis(dto);
            return ResponseApi.ok(vo);
        } catch (Exception e) {  //捕捉入参校验异常
            return ResponseApi.fail("业务系统风险分析失败");
        }

    }

    /**
     * 导出word
     */
    @ApiOperation(value = "导出word")
    @PostMapping("/exportWord")
    public void exportWord(HttpServletResponse response, @RequestBody RequestModel<ExportWordDto> dto) throws Exception {
        coViewCustomerService.exportWord(response, dto.getPrivator());
    }

    /**
     * 导出资产清单word
     */
    @ApiOperation(value = "导出资产清单word")
    @PostMapping("/exportWordAttachment")
    public void exportReportAttachment(HttpServletResponse response, @RequestBody RequestModel<ExportWordDto> dto) throws Exception {
        coViewCustomerService.exportReportAttachment(response, dto.getPrivator());
    }

    @GetMapping("/detail")
    @ApiOperation(value = "查看客户详情")
    public R<CustomerDetailVO> customerDetail(@ApiParam("客户id") @RequestParam String customerId) {
        QueryCustomerDetailsViewDto dto = new QueryCustomerDetailsViewDto();
        dto.setCustomerId(customerId);
        return R.success(coViewCustomerService.customerDetail(dto));
    }

    /**
     * 业务系统风险分析报告
     */
    @ApiOperation(value = "业务系统风险分析报告")
    @PostMapping(value = "/operationReport/riskAnalysis/queryLifecycleAnalysis")
    public ResponseApi<RiskAnalysisReportVO> queryRiskAnalysisReport(@RequestBody RequestModel<QueryViewLifecycleAnalysisDto> dto) {
        try {
            RiskAnalysisReportVO list = coViewCustomerService.queryRiskAnalysisReport(dto);
            return ResponseApi.ok(list);
        } catch (Exception e) {  //捕捉入参校验异常
            log.error("获取业务系统风险分析报告失败!", e);
            return ResponseApi.fail("获取业务系统风险分析报告失败");
        }

    }

    @GetMapping("/export/word/precheck")
    @ApiOperation(value = "导出报告前校验")
    @PreAuthorize("@ss.hasPermi('operation:exportReport')")
    public void preCheck() {

    }


    /**
     * 能力差距分析--符合情况分析
     */
    @ApiOperation(value = "能力差距分析--结果分析查询")
    @GetMapping(value = "/operationReport/gapAnalysis/queryGapChartResult")
    public ResponseApi<QueryViewGapAnalysisConformVo> queryGapChartResult(@ApiParam("作业ID") @RequestParam("operationId") String operationId) {
        return ResponseApi.ok(coViewCustomerService.queryGapChartResult(operationId));
    }

    /**
     * 能力差距分析--符合情况分析
     */
    @ApiOperation(value = "专项评估能力差距分析--结果分析查询")
    @GetMapping(value = "/operationReport/gapAnalysis/spec/queryGapChartResult")
    public ResponseApi<QueryViewGapAnalysisConformVo> querySpecGapChartResult(@ApiParam("作业ID") @RequestParam("operationId") String operationId) {
        return ResponseApi.ok(coViewCustomerService.querySpecGapChartResult(operationId));
    }


    /**
     * 个人专项报告查看-总览
     */
    @ApiOperation(value = "专项评估报告查看-总览")
    @GetMapping(value = "/operationReport/spec/overview")
    public ResponseApi<SpecOverviewVO> getSpecOverview(@ApiParam("作业ID") @RequestParam("operationId") String operationId) {
        return ResponseApi.ok(coViewCustomerService.getSpecOverview(operationId));
    }

    /**
     * 个人专项报告查看-总览
     */
    @ApiOperation(value = "客户视图-统计该客户业务系统、资产以及数据库用户权限数量")
    @GetMapping(value = "/data/statistics/overview")
    public ResponseApi<CustomerOverviewVO> getDataOverview(@ApiParam("客户ID") @RequestParam(value = "customerId") String customerId) {
        return ResponseApi.ok(coViewCustomerService.getDataOverview(customerId));
    }


    /**
     * 能力市场-查询技术能力报告
     */
    @ApiOperation(tags = "能力市场", value = "查询技术能力报告")
    @GetMapping(value = "/tec/report")
    public ResponseApi<ApiTecReportVO> tecReportQuery(@ApiParam("作业ID") @RequestParam(value = "operationId") String operationId) {
        return ResponseApi.ok(coViewCustomerService.tecReportQuery(operationId));
    }
}
